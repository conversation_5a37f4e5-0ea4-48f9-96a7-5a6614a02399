<?php
// File controller xử lý thêm sản phẩm vào giỏ hàng

// Bao gồm file cấu hình cơ sở dữ liệu và khởi tạo session
include('../config.php');

// Kiểm tra xem có dữ liệu book_id được gửi từ form không (thêm sản phẩm mới)
if(isset($_POST['book_id'])) {
    // Bao gồm file model kiểm tra đăng nhập
    include('../model/checkLogin_model.php');

    // Lấy ID sách từ dữ liệu POST
    $book_id = $_POST['book_id'];

    // Lấy ID khách hàng từ session
    $customer_id = $_SESSION['customer_id'];

    // Lấy số lượng sách muốn thêm từ dữ liệu POST
    $quantity = $_POST['quantity'];

    // Tạo câu truy vấn kiểm tra xem sách đã có trong giỏ hàng chưa
    $book = "SELECT * FROM `cart` WHERE book_id=$book_id AND customer_id=$customer_id";

    // Thực thi câu truy vấn
    $book_run = mysqli_query($conn,$book);

    // Kiểm tra xem sách đã có trong giỏ hàng chưa
    if(mysqli_num_rows($book_run) > 0 ){
        // Nếu sách đã có trong giỏ hàng, lấy thông tin hiện tại
        $row = mysqli_fetch_array($book_run);

        // Tính số lượng mới = số lượng hiện tại + số lượng muốn thêm
        $newQuantity = $quantity + $row['quantity'];

        // Tạo câu truy vấn cập nhật số lượng trong giỏ hàng
        $add_cart = "UPDATE cart SET quantity = $newQuantity WHERE book_id= $book_id AND customer_id=$customer_id";

        // Thực thi câu truy vấn cập nhật
        $add_cart_run = mysqli_query($conn,$add_cart);

        // Kiểm tra xem cập nhật có thành công không
        if(!$add_cart_run){
            // Nếu thất bại, chuyển hướng về trang chủ
            header('location: ../customer/home.php');
            exit(0); // Dừng thực thi script
        }
    }else{
        // Nếu sách chưa có trong giỏ hàng, thêm mới
        $add_cart = "INSERT INTO cart (customer_id,book_id,quantity) VALUE ($customer_id,$book_id,$quantity)";

        // Thực thi câu truy vấn thêm mới
        $add_cart_run = mysqli_query($conn,$add_cart);

        // Kiểm tra xem thêm mới có thành công không
        if(!$add_cart_run){
            // Nếu thất bại, chuyển hướng về trang chủ
            header('location: ../customer/home.php');
            exit(0); // Dừng thực thi script
        }
    }
}
// Kiểm tra xem có yêu cầu thay đổi số lượng sản phẩm không
if(isset($_POST['change_quantity'])){
    // Bao gồm file model kiểm tra đăng nhập
    include('../model/checkLogin_model.php');

    // Lấy số lượng mới từ dữ liệu POST
    $quantity = $_POST['change_quantity'];

    // Lấy ID sách từ dữ liệu POST
    $book_id = $_POST['book_id'];

    // Lấy ID khách hàng từ session
    $customer_id = $_SESSION['customer_id'];

    // Tạo câu truy vấn cập nhật số lượng sản phẩm trong giỏ hàng
    $change_quantity = "UPDATE cart SET quantity = $quantity WHERE book_id= $book_id AND customer_id =$customer_id";

    // Thực thi câu truy vấn cập nhật
    $change_quantity_run = mysqli_query($conn,$change_quantity);

    // Kiểm tra xem cập nhật có thành công không
    if(!$change_quantity_run){
        // Nếu thất bại, chuyển hướng về trang chủ
        header('location: ../customer/home.php');
        exit(0); // Dừng thực thi script
    }
}
// Kết thúc file PHP