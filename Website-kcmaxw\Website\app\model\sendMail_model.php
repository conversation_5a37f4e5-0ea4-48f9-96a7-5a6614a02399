<?php
// File model chứa các hàm gửi email sử dụng PHPMailer

// Import các class cần thiết từ PHPMailer
use PHPMailer\PHPMailer\PHPMailer; // Class chính để gửi email
use PHPMailer\PHPMailer\SMTP; // Class xử lý SMTP
use PHPMailer\PHPMailer\Exception; // Class xử lý ngoại lệ

// Tải autoloader của Composer để sử dụng các thư viện đã cài đặt
require '../../../../vendor/autoload.php';

// Hàm gửi email xác thực tài khoản
function sendmail_Verify($email, $code){
    // Tạo đối tượng PHPMailer mới với chế độ exception
    $mail = new PHPMailer(true);

    // Cấu hình sử dụng SMTP để gửi email
    $mail->isSMTP();

    // Đặt máy chủ SMTP của Gmail
    $mail->Host       = 'smtp.gmail.com';

    // B<PERSON>t xác thực SMTP
    $mail->SMTPAuth   = true;

    // Tên đăng nhập SMTP (email gửi)
    $mail->Username   = '<EMAIL>';

    // Mật khẩu ứng dụng Gmail (App Password)
    $mail->Password   = 'tflpnhxaxckznksk';

    // Sử dụng mã hóa TLS ngầm định
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;

    // Cổng SMTP cho TLS ngầm định (465)
    $mail->Port       = 465;

    // Thiết lập người gửi email
    $mail->setFrom('<EMAIL>');

    // Thêm địa chỉ email người nhận
    $mail->addAddress($email);

    // Đặt định dạng email là HTML
    $mail->isHTML(true);

    // Đặt tiêu đề email
    $mail->Subject = 'noreply';

    // Tạo nội dung email HTML
    $text = "
        <h2>Bạn đã đăng kí tài khoản ở Bookstore</h2>
        <h5>Xác thực tài khoản bằng cánh ấn link dưới</h5>
        <br><br>
        <a href='http://localhost:88/website/app/controller/verify.php?code=$code'>Click me</a>
    ";

    // Đặt nội dung email
    $mail->Body    = $text;

    // Gửi email
    $mail->send();
}

// Hàm gửi email đặt lại mật khẩu
function sendmail_pass_reset($email, $code){
    // Tạo đối tượng PHPMailer mới với chế độ exception
    $mail = new PHPMailer(true);

    // Cấu hình sử dụng SMTP để gửi email
    $mail->isSMTP();

    // Đặt máy chủ SMTP của Gmail
    $mail->Host       = 'smtp.gmail.com';

    // Bật xác thực SMTP
    $mail->SMTPAuth   = true;

    // Tên đăng nhập SMTP (email gửi)
    $mail->Username   = '<EMAIL>';

    // Mật khẩu ứng dụng Gmail (App Password)
    $mail->Password   = 'tflpnhxaxckznksk';

    // Sử dụng mã hóa TLS ngầm định
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;

    // Cổng SMTP cho TLS ngầm định (465)
    $mail->Port       = 465;

    // Thiết lập người gửi email
    $mail->setFrom('<EMAIL>');

    // Thêm địa chỉ email người nhận
    $mail->addAddress($email);

    // Đặt định dạng email là HTML
    $mail->isHTML(true);

    // Đặt tiêu đề email
    $mail->Subject = 'noreply';

    // Tạo nội dung email HTML cho đặt lại mật khẩu
    $text = "
        <h2>Xin chào bạn</h2>
        <h5>Bạn Đã Yêu Cầu Đổi Mật Khẩu , Hãy Nhấn Vào Đây.</h5>
        <br><br>
        <a href='http://localhost:88/website/app/views/LoginAndSignup/resetPassword.php?code=$code'>Click me</a>
    ";

    // Đặt nội dung email
    $mail->Body    = $text;

    // Gửi email
    $mail->send();
}
