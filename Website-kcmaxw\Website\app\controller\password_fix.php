<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

include('../../app/config.php');

// <PERSON>ể<PERSON> tra kết nối cơ sở dữ liệu
echo "<h3>Kiểm tra kết nối cơ sở dữ liệu:</h3>";
if ($conn->connect_error) {
    die("Kết nối thất bại: " . $conn->connect_error);
} else {
    echo "Kết nối thành công đến cơ sở dữ liệu: " . $database . "<br>";
}

// Kiểm tra dữ liệu trong bảng customer
echo "<h3>Dữ liệu trong bảng customer:</h3>";
$result = $conn->query("SELECT customer_id, email, name, pass, LENGTH(pass) as pass_length, verify_status FROM customer");
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Email</th><th>Name</th><th>Password</th><th>Password Length</th><th>Verify Status</th><th>Is Hashed</th></tr>";
    while ($row = $result->fetch_assoc()) {
        $isHashed = (strlen($row['pass']) > 20) ? "Có" : "Không";
        echo "<tr>";
        echo "<td>" . $row['customer_id'] . "</td>";
        echo "<td>" . $row['email'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . htmlspecialchars(substr($row['pass'], 0, 20)) . "...</td>";
        echo "<td>" . $row['pass_length'] . "</td>";
        echo "<td>" . $row['verify_status'] . "</td>";
        echo "<td>" . $isHashed . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Không thể lấy dữ liệu từ bảng customer: " . $conn->error . "<br>";
}

// Đặt lại mật khẩu cho tài khoản cụ thể
if (isset($_POST['reset_password'])) {
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    $new_password = mysqli_real_escape_string($conn, $_POST['new_password']);
    
    // Kiểm tra email
    $check_sql = "SELECT * FROM customer WHERE email='$email'";
    $check_result = mysqli_query($conn, $check_sql);
    
    if (mysqli_num_rows($check_result) === 1) {
        // Cập nhật mật khẩu (không mã hóa)
        $update_sql = "UPDATE customer SET pass='$new_password', verify_status=1 WHERE email='$email'";
        if (mysqli_query($conn, $update_sql)) {
            echo "<div style='color: green; font-weight: bold;'>Đặt lại mật khẩu thành công!</div>";
            
            // Hiển thị thông tin tài khoản sau khi cập nhật
            $account_sql = "SELECT * FROM customer WHERE email='$email'";
            $account_result = mysqli_query($conn, $account_sql);
            if (mysqli_num_rows($account_result) === 1) {
                $row = mysqli_fetch_assoc($account_result);
                echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>Thông tin tài khoản sau khi cập nhật:</h4>";
                echo "<p><strong>ID:</strong> " . $row['customer_id'] . "</p>";
                echo "<p><strong>Email:</strong> " . $row['email'] . "</p>";
                echo "<p><strong>Tên:</strong> " . $row['name'] . "</p>";
                echo "<p><strong>Mật khẩu:</strong> " . htmlspecialchars($row['pass']) . "</p>";
                echo "<p><strong>Độ dài mật khẩu:</strong> " . strlen($row['pass']) . " ký tự</p>";
                echo "<p><strong>Trạng thái xác thực:</strong> " . ($row['verify_status'] == 1 ? 'Đã xác thực' : 'Chưa xác thực') . "</p>";
                echo "</div>";
                
                echo "<div style='background-color: #e9f7ef; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>Thông tin đăng nhập:</h4>";
                echo "<p><strong>Email:</strong> " . $email . "</p>";
                echo "<p><strong>Mật khẩu:</strong> " . $new_password . "</p>";
                echo "</div>";
            }
        } else {
            echo "<div style='color: red; font-weight: bold;'>Lỗi khi cập nhật mật khẩu: " . mysqli_error($conn) . "</div>";
        }
    } else {
        echo "<div style='color: red; font-weight: bold;'>Không tìm thấy tài khoản với email: " . $email . "</div>";
    }
}

// Đặt lại mật khẩu cho tất cả tài khoản
if (isset($_POST['reset_all_passwords'])) {
    $default_password = "123456";
    
    // Cập nhật mật khẩu cho tất cả tài khoản (không mã hóa)
    $update_sql = "UPDATE customer SET pass='$default_password', verify_status=1";
    if (mysqli_query($conn, $update_sql)) {
        echo "<div style='color: green; font-weight: bold;'>Đặt lại mật khẩu cho tất cả tài khoản thành công!</div>";
        echo "<div style='background-color: #e9f7ef; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>Thông tin đăng nhập mới cho tất cả tài khoản:</h4>";
        echo "<p><strong>Mật khẩu:</strong> " . $default_password . "</p>";
        echo "</div>";
    } else {
        echo "<div style='color: red; font-weight: bold;'>Lỗi khi cập nhật mật khẩu: " . mysqli_error($conn) . "</div>";
    }
}

// Kiểm tra đăng nhập
if (isset($_POST['test_login'])) {
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    $pass = mysqli_real_escape_string($conn, $_POST['pass']);
    
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>Kiểm tra đăng nhập:</h4>";
    echo "<p><strong>Email nhập vào:</strong> " . $email . "</p>";
    echo "<p><strong>Mật khẩu nhập vào:</strong> " . $pass . "</p>";
    
    // Kiểm tra email
    $sql = "SELECT * FROM customer WHERE email='$email'";
    $result = mysqli_query($conn, $sql);
    
    if (mysqli_num_rows($result) === 1) {
        $row = mysqli_fetch_assoc($result);
        echo "<p style='color: green;'><strong>Tìm thấy email trong cơ sở dữ liệu.</strong></p>";
        echo "<p><strong>Mật khẩu trong DB:</strong> " . htmlspecialchars($row['pass']) . "</p>";
        echo "<p><strong>Độ dài mật khẩu:</strong> " . strlen($row['pass']) . " ký tự</p>";
        
        // Kiểm tra mật khẩu
        if ($pass === $row['pass']) {
            echo "<p style='color: green;'><strong>Mật khẩu khớp khi so sánh trực tiếp!</strong></p>";
            
            // Kiểm tra trạng thái xác thực
            if ($row['verify_status'] == 1) {
                echo "<p style='color: green;'><strong>Tài khoản đã được xác thực.</strong></p>";
                echo "<p style='color: green; font-weight: bold;'>Đăng nhập thành công!</p>";
            } else {
                echo "<p style='color: red;'><strong>Tài khoản chưa được xác thực.</strong></p>";
            }
        } else {
            echo "<p style='color: red;'><strong>Mật khẩu không khớp khi so sánh trực tiếp!</strong></p>";
            
            // Thử kiểm tra bằng password_verify
            if (password_verify($pass, $row['pass'])) {
                echo "<p style='color: green;'><strong>Mật khẩu khớp khi sử dụng password_verify!</strong></p>";
                
                // Kiểm tra trạng thái xác thực
                if ($row['verify_status'] == 1) {
                    echo "<p style='color: green;'><strong>Tài khoản đã được xác thực.</strong></p>";
                    echo "<p style='color: green; font-weight: bold;'>Đăng nhập thành công!</p>";
                } else {
                    echo "<p style='color: red;'><strong>Tài khoản chưa được xác thực.</strong></p>";
                }
            } else {
                echo "<p style='color: red;'><strong>Mật khẩu không khớp khi sử dụng password_verify!</strong></p>";
            }
        }
    } else {
        echo "<p style='color: red;'><strong>Không tìm thấy email trong cơ sở dữ liệu.</strong></p>";
    }
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sửa lỗi mật khẩu</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 20px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .form-container {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        input[type="text"], input[type="password"], input[type="email"] {
            padding: 8px;
            width: 300px;
            margin-bottom: 10px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h2>Công cụ sửa lỗi mật khẩu</h2>
    
    <div class="warning">
        <h3>Lưu ý quan trọng:</h3>
        <p>Công cụ này sẽ đặt lại mật khẩu về dạng <strong>không mã hóa</strong> để giải quyết vấn đề đăng nhập. Điều này không an toàn cho môi trường sản xuất, chỉ nên sử dụng cho mục đích phát triển và kiểm thử.</p>
    </div>
    
    <div class="form-container">
        <h3>1. Đặt lại mật khẩu cho tài khoản cụ thể</h3>
        <form method="post" action="">
            <div>
                <label for="email">Email:</label><br>
                <input type="email" name="email" value="<EMAIL>" required>
            </div>
            <div>
                <label for="new_password">Mật khẩu mới:</label><br>
                <input type="text" name="new_password" value="123456" required>
            </div>
            <button type="submit" name="reset_password">Đặt lại mật khẩu</button>
        </form>
    </div>
    
    <div class="form-container">
        <h3>2. Đặt lại mật khẩu cho tất cả tài khoản</h3>
        <p>Đặt lại mật khẩu cho tất cả tài khoản thành "123456" và đặt trạng thái xác thực thành 1.</p>
        <form method="post" action="">
            <button type="submit" name="reset_all_passwords">Đặt lại tất cả mật khẩu</button>
        </form>
    </div>
    
    <div class="form-container">
        <h3>3. Kiểm tra đăng nhập</h3>
        <form method="post" action="">
            <div>
                <label for="email">Email:</label><br>
                <input type="email" name="email" value="<EMAIL>" required>
            </div>
            <div>
                <label for="pass">Mật khẩu:</label><br>
                <input type="password" name="pass" value="123456" required>
            </div>
            <button type="submit" name="test_login">Kiểm tra đăng nhập</button>
        </form>
    </div>
</body>
</html>
