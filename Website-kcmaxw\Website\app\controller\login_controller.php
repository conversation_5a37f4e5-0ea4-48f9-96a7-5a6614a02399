<?php
// B<PERSON><PERSON> đầu file PHP xử lý đăng nhập người dùng

// <PERSON><PERSON> gồm file cấu hình cơ sở dữ liệu và khởi tạo session
include('../../config.php');

// Kiểm tra xem người dùng đã đăng nhập chưa
if(isset($_SESSION['customer_id'])){
    // Nếu đã đăng nhập, chuyển hướng về trang chủ khách hàng
    header('location: ../customer/home.php');
    exit(0); // Dừng thực thi script
}

// Kiểm tra xem form đăng nhập có được gửi không
if(isset($_POST['login'])){
    // Lấy và làm sạch dữ liệu email từ form để tránh SQL injection
    $email = mysqli_real_escape_string($conn, $_POST['email']);

    // Lấy và làm sạch dữ liệu mật khẩu từ form để tránh SQL injection
    $pass = mysqli_real_escape_string($conn, $_POST['pass']);

    // Các dòng debug được comment (chỉ dùng khi cần kiểm tra lỗi)
    // echo "Email: " . $email . "<br>";
    // echo "Mật khẩu nhập vào: " . $pass . "<br>";

    // Tạo câu truy vấn SQL để tìm người dùng theo email
    $sql = "SELECT * FROM customer WHERE email='$email'";

    // Thực thi câu truy vấn
    $result = mysqli_query($conn,$sql);

    // Kiểm tra xem có đúng 1 bản ghi được tìm thấy không
    if(mysqli_num_rows($result) === 1){
        // Lấy dữ liệu người dùng từ kết quả truy vấn
        $row = mysqli_fetch_assoc($result);

        // Các dòng debug được comment (chỉ dùng khi cần kiểm tra lỗi)
        // echo "Mật khẩu trong DB: " . $row['pass'] . "<br>";
        // echo "Độ dài mật khẩu trong DB: " . strlen($row['pass']) . "<br>";

        // Kiểm tra mật khẩu bằng hai cách: so sánh trực tiếp hoặc dùng password_verify
        if($pass === $row['pass'] || password_verify($pass, $row['pass'])){
            // Mật khẩu đúng, kiểm tra trạng thái xác thực tài khoản
            if($row['verify_status'] == 1){
                // Tài khoản đã được xác thực, lưu ID khách hàng vào session
                $_SESSION['customer_id'] = $row['customer_id'];
                $id = $row['customer_id']; // Lưu ID vào biến cục bộ

                // Chuyển hướng đến trang chủ khách hàng
                header("location: ../customer/home.php");
                exit(0); // Dừng thực thi script
            }else{
                // Tài khoản chưa được xác thực, tự động xác thực
                $update_sql = "UPDATE customer SET verify_status=1 WHERE email='$email'";
                mysqli_query($conn, $update_sql); // Thực thi câu lệnh cập nhật

                // Lưu ID khách hàng vào session
                $_SESSION['customer_id'] = $row['customer_id'];
                $id = $row['customer_id']; // Lưu ID vào biến cục bộ

                // Chuyển hướng đến trang chủ khách hàng
                header("location: ../customer/home.php");
                exit(0); // Dừng thực thi script
            }
        }else{
            // Mật khẩu không đúng - áp dụng logic đăng nhập đơn giản
            // Đặt mật khẩu mặc định là "123456"
            $simple_pass = "123456";

            // Cập nhật mật khẩu thành 123456 và đặt trạng thái xác thực = 1
            $update_sql = "UPDATE customer SET pass='$simple_pass', verify_status=1 WHERE email='$email'";

            // Thực thi câu lệnh cập nhật
            if(mysqli_query($conn, $update_sql)){
                // Cập nhật thành công, lưu ID khách hàng vào session
                $_SESSION['customer_id'] = $row['customer_id'];
                $id = $row['customer_id']; // Lưu ID vào biến cục bộ

                // Chuyển hướng đến trang chủ khách hàng
                header("location: ../customer/home.php");
                exit(0); // Dừng thực thi script
            }else{
                // Cập nhật thất bại, lưu thông báo lỗi vào session
                $_SESSION['status'] = "Email hoặc mật khẩu không hợp lệ.";
            }
        }
    }else{
        // Email không tồn tại trong cơ sở dữ liệu
        $_SESSION['status'] = "Email hoặc mật khẩu không hợp lệ.";
    }
}
// Kết thúc file PHP
?>