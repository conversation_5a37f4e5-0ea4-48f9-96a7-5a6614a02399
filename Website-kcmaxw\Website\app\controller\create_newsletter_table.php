<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Connect to the database directly
$servername = "localhost";
$username = "root";
$password = "";
$database = "dulieusach";

// Create connection
$conn = new mysqli($servername, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Check if the newsletter_subscribers table exists
$tableExists = false;
$result = $conn->query("SHOW TABLES LIKE 'newsletter_subscribers'");
if ($result->num_rows > 0) {
    $tableExists = true;
}

// If the table doesn't exist, create it
if (!$tableExists) {
    $sql = "CREATE TABLE newsletter_subscribers (
        id INT(11) NOT NULL AUTO_INCREMENT,
        email VARCHAR(255) NOT NULL,
        subscribe_date DATETIME NOT NULL,
        PRIMARY KEY (id),
        UNIQUE KEY (email)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if ($conn->query($sql) === TRUE) {
        echo "Table newsletter_subscribers created successfully";
    } else {
        echo "Error creating table: " . $conn->error;
    }
} else {
    echo "Table newsletter_subscribers already exists";
}

$conn->close();
?>
