// Hàm định dạng số tiền theo kiểu Việt Nam (thêm dấu chấm phân cách hàng nghìn và ký hiệu đồng)
function format1(n) {
  // <PERSON><PERSON> báo ký hiệu tiền tệ
  var currency = "đ";

  // Trả về số đã được định dạng
  return (
    // Làm tròn số về số nguyên và thay thế từng ký tự
    n.toFixed(0).replace(/./g, function (c, i, a) {
      // Thêm dấu chấm trước mỗi nhóm 3 chữ số (trừ ký tự đầu tiên và dấu chấm)
      return i > 0 && c !== "." && (a.length - i) % 3 === 0 ? "." + c : c;
    }) + currency // Thêm ký hiệu tiền tệ vào cuối
  );
}

// Chờ DOM được tải hoàn toàn trước khi thực thi các sự kiện
$(document).ready(function () {
  // <PERSON><PERSON> lý sự kiện click cho nút toggle menu
  $(".menu-toggle").click(function () {
    // Dòng debug được comment
    // console.log($(".menu").css("display"));

    // Kiểm tra trạng thái hiển thị của menu
    if ($(".menu").css("opacity") == "0") {
      // Nếu menu đang ẩn (opacity = 0), hiển thị menu
      $(".menu").css("opacity", "1");
    } else {
      // Nếu menu đang hiển thị, ẩn menu
      $(".menu").css("opacity", "0");
    }
  });

  // Xử lý sự kiện click cho menu wrapper (chức năng debug)
  $(".menu-wrapper").click(function () {
    // In thông báo debug ra console
    console.log("tstestsets");
  });

  // Xử lý sự kiện click cho nút hiển thị modal số điện thoại
  $("#phoneBtn").on("click", function () {
    // Hiển thị modal backdrop cho số điện thoại
    $("#phoneBackdrop").modal("show");
  });

  // Xử lý sự kiện click cho nút hiển thị modal email
  $("#emailBtn").on("click", function () {
    // Hiển thị modal backdrop cho email
    $("#emailBackdrop").modal("show");
  });

  // Xử lý sự kiện click cho nút toggle hiển thị bình luận
  $(".comment-toggle").click((event) => {
    // Lấy ký tự thứ 5 từ ID của element được click (để xác định số thứ tự comment)
    let i = event.currentTarget.id.charAt(4);

    // In thông tin debug ra console
    console.log(event.currentTarget);
    console.log(event.currentTarget.id.charAt(4));

    // Kiểm tra trạng thái hiển thị của comment tương ứng
    if ($(`#comment${i}`).css("display") == "none") {
      // Nếu comment đang ẩn, hiển thị nó
      $(`#comment${i}`).css("display", "block");
    } else {
      // Nếu comment đang hiển thị, ẩn nó
      $(`#comment${i}`).css("display", "none");
    }
  });

  // Xử lý sự kiện thay đổi số lượng sản phẩm trong giỏ hàng
  $(".itemQty").on("change", function () {
    // Tìm element div cha gần nhất chứa thông tin sản phẩm
    var $el = $(this).closest("div");

    // Lấy ID sách từ input ẩn
    var book_id = $el.find(".book_id").val();

    // Lấy số lượng mới từ input
    var quantity = $el.find(".itemQty").val();

    // Gửi yêu cầu AJAX để cập nhật số lượng
    $.ajax({
      url: "../../controller/addCart_ctl.php", // Đường dẫn đến controller xử lý
      method: "POST", // Phương thức HTTP POST
      cache: false, // Không cache kết quả
      data: { change_quantity: quantity, book_id: book_id }, // Dữ liệu gửi đi
      success: function () {
        // Khi cập nhật thành công, tải lại trang
        location.reload(true);
      },
    });
  });
  // Xử lý sự kiện click cho checkbox dịch vụ
  $(".service").on("click", function () {
    // Khởi tạo giá dịch vụ = 0
    var service = 0;

    // Kiểm tra tất cả checkbox dịch vụ được chọn
    $(".service:checked").each(function () {
      // Nếu có checkbox được chọn, đặt giá dịch vụ = 20.000đ
      service = 20000;
    });

    // Tính tổng giá = giá dịch vụ + giá sản phẩm
    var tt_price = service + Number($("#it-price").val());

    // Hiển thị giá dịch vụ đã định dạng
    $("#dp-sv-price").html(format1(service));

    // Hiển thị tổng giá đã định dạng
    $("#dp-total-price").html(format1(tt_price));

    // Cập nhật giá trị tổng giá vào input ẩn
    $("#total-price").val(tt_price);
  });

  // Xử lý sự kiện click cho nút về trang chủ
  $(".home").on("click", function () {
    // Chuyển hướng về trang chủ khách hàng
    location.replace("../../views/customer/home.php");
  });

  // Xử lý sự kiện click cho nút thêm bình luận mới
  $("#newComment-bt").on("click", function () {
    // Lấy nội dung bình luận từ textarea
    var comment = $("#newComment").val();

    // Lấy ID sách từ input ẩn
    var book_id = $("#book-id").val();

    // Gửi yêu cầu AJAX để thêm bình luận
    $.ajax({
      url: "../../controller/addcmt_ctl.php", // Đường dẫn đến controller xử lý bình luận
      method: "POST", // Phương thức HTTP POST
      cache: false, // Không cache kết quả
      data: { comment: comment, book_id: book_id }, // Dữ liệu gửi đi
      success: function () {
        // Khi thêm bình luận thành công, tải lại trang
        location.reload(true);
      },
    });
  });
  // Xử lý sự kiện thay đổi cho input số lượng bọc sách và checkbox dịch vụ bọc sách
  $("#book-wrap-quantity, #flexSwitchCheckChecked").on("input change", function () {
    // Kiểm tra xem checkbox dịch vụ bọc sách có được chọn không
    var isChecked = $("#flexSwitchCheckChecked").is(":checked");

    // Lấy số lượng bọc sách (nếu checkbox được chọn) hoặc 0 (nếu không chọn)
    var quantity = isChecked ? Number($("#book-wrap-quantity").val()) : 0;

    // Tính phí dịch vụ bọc sách: số lượng × 20.000đ
    var service = quantity * 20000;

    // Tính tổng giá = phí dịch vụ + giá sản phẩm
    var tt_price = service + Number($("#it-price").val());

    // Hiển thị phí bọc sách đã định dạng
    $("#book-wrap-fee").html(format1(service));

    // Hiển thị phí dịch vụ đã định dạng
    $("#dp-sv-price").html(format1(service));

    // Hiển thị tổng giá đã định dạng
    $("#dp-total-price").html(format1(tt_price));

    // Cập nhật giá trị tổng giá vào input ẩn
    $("#total-price").val(tt_price);
  });

  // Xử lý sự kiện thay đổi riêng cho input số lượng bọc sách (kiểm tra giới hạn)
  $("#book-wrap-quantity").on("input change", function () {
    // Kiểm tra xem checkbox dịch vụ bọc sách có được chọn không
    var isChecked = $("#flexSwitchCheckChecked").is(":checked");

    // Lấy số lượng bọc sách từ input
    var quantity = Number($(this).val());

    // Lấy số lượng tối đa cho phép từ thuộc tính max
    var maxQuantity = $(this).attr("max");

    // Kiểm tra xem số lượng có vượt quá giới hạn không
    if (quantity > maxQuantity) {
      // Hiển thị cảnh báo nếu vượt quá giới hạn
      alert("Số Bọc Sách Không Được Mua Quá Số Lượng Sản Phẩm");

      // Đặt lại giá trị về số lượng tối đa
      $(this).val(maxQuantity);
      quantity = maxQuantity; // Cập nhật biến quantity
    }

    // Tính phí dịch vụ bọc sách (chỉ khi checkbox được chọn)
    var service = isChecked ? quantity * 20000 : 0;

    // Tính tổng giá = phí dịch vụ + giá sản phẩm
    var tt_price = service + Number($("#it-price").val());

    // Hiển thị phí bọc sách đã định dạng
    $("#book-wrap-fee").html(format1(service));

    // Hiển thị phí dịch vụ đã định dạng
    $("#dp-sv-price").html(format1(service));

    // Hiển thị tổng giá đã định dạng
    $("#dp-total-price").html(format1(tt_price));

    // Cập nhật giá trị tổng giá vào input ẩn
    $("#total-price").val(tt_price);
  });
});
// Kết thúc khối jQuery document ready
