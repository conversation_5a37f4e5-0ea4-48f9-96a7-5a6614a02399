function addCart(book_id){
    quantity = $("#set-quantity").val();
    $.ajax({
        type: "POST",
        url: '../../controller/addCart_ctl.php',
        data: {quantity:quantity,book_id:book_id},
        cache: false,
        success: function(data){
            alert("Thêm vào giỏ hàng thành công");
        }
    });
}
function buynow(book_id){
    quantity = $("#set-quantity").val();
    $.ajax({
        type: "POST",
        url: '../../controller/addCart_ctl.php',
        cache: false,
        data: {quantity:quantity,book_id:book_id},
        success: function(){
            location.replace('../../views/checkout/cart.php');
        }
    });
}
$(document).ready(function() {
  $("#set-quantity").attr("max", 10);
  $("#set-quantity").on("input", function() {
    let val = parseInt($(this).val());
    if (val > 10) {
      $(this).val(10);
      alert("Hàng trong kho không còn quá 10 sản phẩm hãy lựa chọn số lượng sản phẩm phù hợp");
    }
  });
});