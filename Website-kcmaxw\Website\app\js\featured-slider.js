document.addEventListener('DOMContentLoaded', function () {
  // <PERSON><PERSON>y danh sách các item
  const container = document.querySelector('.featured-products-list');
  if (!container) return;
  // <PERSON><PERSON><PERSON> tất cả sản phẩm con
  let items = Array.from(container.children);
  // Xáo trộn mảng sản phẩm
  for (let i = items.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [items[i], items[j]] = [items[j], items[i]];
  }
  // Xóa các sản phẩm cũ và thêm lại theo thứ tự random
  container.innerHTML = '';
  items.forEach(item => container.appendChild(item));

  // Khởi tạo Swiper
  new Swiper('.featured-products-swiper', {
    slidesPerView: 5,
    spaceBetween: 24,
    loop: true,
    autoplay: {
      delay: 2500,
      disableOnInteraction: false,
    },
    navigation: {
      nextEl: '.swiper-button-next',
      prevEl: '.swiper-button-prev',
    },
    pagination: {
      el: '.swiper-pagination',
      clickable: true,
    },
    breakpoints: {
      1200: { slidesPerView: 5 },
      992: { slidesPerView: 4 },
      768: { slidesPerView: 3 },
      480: { slidesPerView: 2 },
      0: { slidesPerView: 1 }
    }
  });
});
