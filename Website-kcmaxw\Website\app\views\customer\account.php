<?php
include '../../controller/ctmAccount_ctl.php';
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bookstore</title>
    <link href="//cdn.muicss.com/mui-0.10.3/css/mui.min.css" rel="stylesheet" type="text/css" />
    <script src="//cdn.muicss.com/mui-0.10.3/js/mui.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
    <link rel="stylesheet" href="../../../public/css/header.css">
    <link rel="stylesheet" href="../../../public/css/footer.css">
    <link rel="stylesheet" href="../../../public/css/account/account.css">
    <link rel="stylesheet" href="../../../public/css/account/sidebar.css">
    <link rel="stylesheet" href="../../../public/css/mobile.css">

    <script src="https://kit.fontawesome.com/9d371022aa.js" crossorigin="anonymous"></script>
</head>

<body>
    <?php include '../components/header.php'; ?>
    <div class="main-container">
        <div class="container-fluid content">
            <div class="row">
                <div class="col-3">
                    <?php include '../components/customer-sidebar.php'; ?>
                </div>
                <div class="col-9">
                    <div class="profile-container">
                        <div class="profile-header">
                            <h2 class="profile-title">THÔNG TIN TÀI KHOẢN</h2>
                            <a href="../../controller/logout_controller.php" class="logout-link">
                                <button type="button" class="btn-logout">
                                    <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                                </button>
                            </a>
                        </div>

                        <?php
                        // Hiển thị thông báo lỗi
                        if (!empty($mess)) {
                            echo "
                                <div class='alert alert-danger' role='alert'>
                                <i class='fas fa-exclamation-circle me-2'></i>$mess
                                </div>
                                ";
                        }

                        // Hiển thị thông báo thành công
                        if (isset($_SESSION['status'])) {
                            $p = $_SESSION['status'];
                            echo "
                                <div class='alert alert-success' role='alert'>
                                <i class='fas fa-check-circle me-2'></i>$p
                                </div>
                                ";
                            unset($_SESSION['status']);
                        }
                        ?>

                        <div class="profile-content">
                            <div class="account-welcome">
                                <h3 class="welcome-title">Thông tin tài khoản</h3>
                                <p class="welcome-subtitle">Xin chào, <?php echo $name; ?></p>
                            </div>

                            <div class="profile-details">
                                <form method="post" action="" class="profile-form">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="name" class="form-label">Họ và Tên*</label>
                                            <div class="input-with-icon">
                                                <i class="fas fa-user input-icon"></i>
                                                <input type="text" class="form-input" name="name" id="name" value="<?php echo $name; ?>" required>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="phone" class="form-label">Số điện thoại*</label>
                                            <div class="input-with-icon">
                                                <i class="fas fa-phone-alt input-icon"></i>
                                                <input type="text" class="form-input" id="phone" name="phone" value="<?php echo $phone_number; ?>" required>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="email" class="form-label">Email*</label>
                                            <div class="input-with-icon">
                                                <i class="fas fa-envelope input-icon"></i>
                                                <input type="email" class="form-input" id="email" name="email" value="<?php echo $email; ?>" required>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="password" class="form-label">Mật khẩu*</label>
                                            <div class="input-with-icon">
                                                <i class="fas fa-lock input-icon"></i>
                                                <input type="password" class="form-input" id="password" name="password" value="<?php echo $password; ?>" required>
                                                <i class="fas fa-eye toggle-password" id="togglePassword"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="btn-save" name="change-value-account">
                                            <i class="fas fa-save me-2"></i>Lưu thay đổi
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php include '../components/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous"></script>
    <script src="../../js/jquery.js"></script>
    <script src="../../js/function.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // Toggle eye icon
            this.classList.toggle('fa-eye');
            this.classList.toggle('fa-eye-slash');
        });

        // Form validation
        const form = document.querySelector('.profile-form');
        const inputs = form.querySelectorAll('input[required]');

        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value.trim() === '') {
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                } else {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            });

            input.addEventListener('input', function() {
                if (this.value.trim() !== '') {
                    this.classList.remove('is-invalid');
                }
            });
        });

        // Email validation
        const emailInput = document.getElementById('email');
        emailInput.addEventListener('blur', function() {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (this.value.trim() !== '' && !emailRegex.test(this.value)) {
                this.classList.add('is-invalid');
                this.classList.remove('is-valid');
            }
        });

        // Phone validation
        const phoneInput = document.getElementById('phone');
        phoneInput.addEventListener('blur', function() {
            const phoneRegex = /^[0-9]{10,11}$/;
            if (this.value.trim() !== '' && !phoneRegex.test(this.value)) {
                this.classList.add('is-invalid');
                this.classList.remove('is-valid');
            }
        });

        // Các chức năng khác có thể được thêm vào đây
    </script>
</body>

</html>