/* Modern Payment Page Styles */
:root {
  --primary: #4A90E2;
  --primary-dark: #3a7bc8;
  --primary-light: #e6f0fb;
  --secondary: #FF6B6B;
  --accent: #6C63FF;
  --success: #28a745;
  --warning: #ffc107;
  --danger: #dc3545;
  --bg-light: #f8fafc;
  --white: #ffffff;
  --text: #333;
  --text-light: #666;
  --border: #e1e8ed;
  --shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  --shadow-hover: 0 15px 35px rgba(0, 0, 0, 0.12);
  --radius: 12px;
  --radius-sm: 8px;
  --transition: all 0.3s ease-in-out;
}

body {
  background: linear-gradient(135deg, var(--bg-light) 0%, #e0e7ff 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text);
}

/* Progress Steps */
.progress-container {
  margin: 20px auto 40px;
  max-width: 600px;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  position: relative;
  margin-bottom: 30px;
}

.progress-steps::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 3px;
  background: #e1e8ed;
  transform: translateY(-50%);
  z-index: 0;
}

.progress-step {
  position: relative;
  z-index: 1;
  background: #fff;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #4A90E2;
  border: 2px solid #4A90E2;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.progress-step.active {
  background: #4A90E2;
  color: #fff;
}

.progress-step-label {
  position: absolute;
  top: 45px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  white-space: nowrap;
  color: #4A90E2;
  font-weight: 500;
}

/* Main Container */
.main-container {
  padding: 30px 0;
}

.cart {
  background-color: var(--white);
  width: 92%;
  max-width: 1200px;
  margin: auto;
  padding: 35px;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: 40px;
  position: relative;
  overflow: hidden;
}

.cart::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary) 0%, var(--accent) 100%);
}

/* Section Titles */
.subtitle {
  font-size: 22px;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
}

.subtitle::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--primary);
  border-radius: 10px;
}

/* Payment Methods */
.payment-methods {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.payment-method {
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background-color: #fff;
}

.payment-method:hover {
  border-color: #4A90E2;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.payment-method.active {
  border-color: #4A90E2;
  background-color: rgba(74, 144, 226, 0.05);
}

.payment-method-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.payment-method-header img {
  width: 40px;
  height: 40px;
  margin-right: 10px;
  object-fit: contain;
}

.payment-method-title {
  font-weight: 600;
  font-size: 16px;
}

.payment-method-description {
  font-size: 14px;
  color: #666;
}

.payment-method-radio {
  position: absolute;
  top: 15px;
  right: 15px;
}

/* Form Inputs */
.form-control {
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  padding: 12px 15px;
  font-size: 15px;
  transition: all 0.3s ease;
  background-color: #f9fafc;
}

.form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
  background-color: #fff;
}

.form-label {
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 8px;
}

/* Order Summary */
.summary .section-wrapper {
  background: linear-gradient(145deg, #f8fbff 0%, #f0f7ff 100%);
  border: 1.5px solid #e3eaf5;
  box-shadow: 0 10px 25px rgba(74,144,226,0.1);
  border-radius: var(--radius);
  position: relative;
  overflow: hidden;
}

.order-summary {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
}

.order-summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 15px;
}

.order-summary-total {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  font-size: 18px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed #e1e8ed;
  color: #e53935;
}

/* Buttons */
.btn {
  padding: 12px 25px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
}

.btn-outline-primary {
  background-color: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
}

.btn-outline-primary:hover {
  background-color: var(--primary-light);
}

.checkout-button {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  gap: 15px;
}

.checkout-button .btn {
  min-width: 200px;
}

/* Responsive */
@media (max-width: 768px) {
  .payment-methods {
    grid-template-columns: 1fr;
  }
  
  .checkout-button {
    flex-direction: column;
  }
  
  .cart {
    padding: 20px;
    width: 95%;
  }
}
