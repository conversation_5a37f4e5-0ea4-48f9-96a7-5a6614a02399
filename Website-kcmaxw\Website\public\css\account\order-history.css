:root {
  --primary: #4A90E2;
  --bg-light: #f1f6f5;
  --white: #ffffff;
  --text: #333;
  --status-bg: #e6f0fb;
  --status-color: #2c6cb7;
  --shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  --radius: 10px;
  --transition: all 0.3s ease-in-out;
}

body {
  background-color: var(--bg-light);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text);
}

.content {
  width: 90%;
  max-width: 1200px;
  margin: 30px auto;
}

.main-container {
  background-color: var(--bg-light);
}

.history {
  background-color: var(--white);
  border-radius: var(--radius);
  margin: 15px 0;
  padding: 1.5em;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.history:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.08);
}

.history-title {
  font-weight: 700;
  font-size: 20px;
  color: var(--primary);
  margin-bottom: 1em;
  border-bottom: 2px solid var(--primary);
  padding-bottom: 0.7em;
  letter-spacing: 0.5px;
}

.history-status {
  background-color: var(--status-bg);
  border-radius: 8px;
  padding: 12px 18px;
  margin-bottom: 20px;
  color: var(--status-color);
  font-weight: 600;
  display: inline-block;
  font-size: 14px;
}

.status-count {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  margin-top: 15px;
}

.status-count p {
  background-color: #f5faff;
  border-left: 4px solid var(--primary);
  padding: 10px 15px;
  border-radius: 6px;
  font-weight: 600;
  color: var(--text);
  margin: 0;
  box-shadow: var(--shadow);
}
