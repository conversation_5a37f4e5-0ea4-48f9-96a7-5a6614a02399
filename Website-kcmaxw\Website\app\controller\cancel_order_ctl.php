<?php
    include('../config.php');
    include('../model/checkLogin_model.php');

    // Hàm trả về kết quả dưới dạng JSON cho AJAX
    function returnJson($success, $message, $data = null) {
        $response = [
            'success' => $success,
            'message' => $message
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        // Đảm bảo không có output nào trước khi gửi header
        if (ob_get_length()) ob_clean();

        // Thiết lập header
        header('Content-Type: application/json');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

        // Trả về JSON
        echo json_encode($response);
        exit;
    }

    // Xác định nếu request là AJAX
    $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

    // Debug
    error_log("AJAX Request: " . ($isAjax ? 'Yes' : 'No'));
    error_log("Request Method: " . $_SERVER['REQUEST_METHOD']);
    if(isset($_POST['order_id'])) {
        error_log("POST order_id: " . $_POST['order_id']);
    }
    if(isset($_GET['order_id'])) {
        error_log("GET order_id: " . $_GET['order_id']);
    }

    // Đảm bảo không có lỗi PHP nào được hiển thị trong response JSON
    error_reporting(0);

    if(isset($_SESSION['customer_id'])) {
        $customer_id = $_SESSION['customer_id'];

        // Lấy order_id từ GET hoặc POST
        $order_id = isset($_POST['order_id']) ? $_POST['order_id'] : (isset($_GET['order_id']) ? $_GET['order_id'] : null);

        // Debug
        error_log("Final order_id: " . ($order_id ? $order_id : "null"));

        if($order_id) {
            // Kiểm tra xem đơn hàng có thuộc về khách hàng này không
            $check_order = "SELECT * FROM `oder` WHERE oder_id='$order_id' AND customer_id='$customer_id' LIMIT 1";
            error_log("SQL Query: " . $check_order);

            $check_order_run = mysqli_query($conn, $check_order);

            if (!$check_order_run) {
                error_log("SQL Error: " . mysqli_error($conn));
                if ($isAjax) {
                    returnJson(false, "Lỗi truy vấn cơ sở dữ liệu: " . mysqli_error($conn));
                } else {
                    $_SESSION['status'] = "Lỗi truy vấn cơ sở dữ liệu";
                    header('location: ../views/customer/order-history.php');
                    exit(0);
                }
            }

            if(mysqli_num_rows($check_order_run) > 0) {
                $order_data = mysqli_fetch_array($check_order_run);

                // Chỉ cho phép hủy đơn hàng khi đơn hàng đang ở trạng thái "Đang xử lý"
                if($order_data['status'] == 1) {
                    // Cập nhật trạng thái đơn hàng thành "Đã hủy" (status = 3)
                    $update_order = "UPDATE `oder` SET status='3' WHERE oder_id='$order_id'";
                    error_log("Update SQL Query: " . $update_order);

                    $update_order_run = mysqli_query($conn, $update_order);

                    if (!$update_order_run) {
                        error_log("Update SQL Error: " . mysqli_error($conn));
                    }

                    if($update_order_run) {
                        if($isAjax) {
                            returnJson(true, "Đơn hàng đã được hủy thành công");
                        } else {
                            $_SESSION['status'] = "Đơn hàng đã được hủy thành công";
                            header('location: ../views/customer/order-history.php');
                            exit(0);
                        }
                    } else {
                        if($isAjax) {
                            returnJson(false, "Có lỗi xảy ra khi hủy đơn hàng");
                        } else {
                            $_SESSION['status'] = "Có lỗi xảy ra khi hủy đơn hàng";
                            header('location: ../views/customer/order-detail.php?oder_id='.$order_id);
                            exit(0);
                        }
                    }
                } else {
                    if($isAjax) {
                        returnJson(false, "Không thể hủy đơn hàng này vì đơn hàng không còn ở trạng thái đang xử lý");
                    } else {
                        $_SESSION['status'] = "Không thể hủy đơn hàng này vì đơn hàng không còn ở trạng thái đang xử lý";
                        header('location: ../views/customer/order-detail.php?oder_id='.$order_id);
                        exit(0);
                    }
                }
            } else {
                if($isAjax) {
                    returnJson(false, "Không tìm thấy đơn hàng");
                } else {
                    $_SESSION['status'] = "Không tìm thấy đơn hàng";
                    header('location: ../views/customer/order-history.php');
                    exit(0);
                }
            }
        } else {
            if($isAjax) {
                returnJson(false, "Thiếu thông tin đơn hàng");
            } else {
                $_SESSION['status'] = "Thiếu thông tin đơn hàng";
                header('location: ../views/customer/order-history.php');
                exit(0);
            }
        }
    } else {
        if($isAjax) {
            returnJson(false, "Vui lòng đăng nhập để thực hiện chức năng này");
        } else {
            header('location: ../views/LoginAndSignup/login.php');
            exit(0);
        }
    }
?>
