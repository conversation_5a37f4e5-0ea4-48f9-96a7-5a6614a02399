@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600&display=swap');

:root {
   --primary-color: #ADD8E6; /* Light blue */
   --secondary-color: #5F9EA0; /* Cadet blue */
   --accent-color: #4682B4; /* Steel blue */
   --text-color: #333;
   --background-color: #FFFFFF; /* White */
   --border-color: #B0C4DE; /* Light steel blue */
   --box-shadow-color: rgba(70, 130, 180, 0.2); /* Steel blue with transparency */
}

*{
   font-family: 'Rubik', sans-serif;
   margin: 0;
   padding: 0;
   box-sizing: border-box;
   outline: none;
   border: none;
   text-decoration: none;
   transition: all .2s linear;
}

*::selection {
   background-color: var(--accent-color);
   color: var(--background-color);
}

*::-webkit-scrollbar {
   height: .5rem;
   width: 1rem;
}

*::-webkit-scrollbar-track {
   background-color: transparent;
}

*::-webkit-scrollbar-thumb {
   background-color: var(--accent-color);
}

html {
   font-size: 62.5%;
   overflow-x: hidden;
}

body {
   background-color: var(--background-color);
   color: var(--text-color);
   font-size: 1.6rem;
}

section {
   padding: 3rem 2rem;
   max-width: 1200px;
   margin: 0 auto;
}

.title {
   text-align: center;
   margin-bottom: 2rem;
   text-transform: uppercase;
   color: var(--text-color);
   font-size: 4rem;
   letter-spacing: 1px;
   font-weight: bold;
}

.empty {
   padding: 1.5rem;
   text-align: center;
   border: 0.1rem solid var(--border-color);
   background-color: var(--background-color);
   color: var(--accent-color);
   font-size: 2rem;
}

.message {
   position: sticky;
   top: 0;
   margin: 0 auto;
   max-width: 1200px;
   background-color: var(--background-color);
   padding: 2rem;
   display: flex;
   align-items: center;
   justify-content: space-between;
   z-index: 10000;
   gap: 1.5rem;
   border-bottom: 0.1rem solid var(--border-color);
}

.message span {
   font-size: 2rem;
   color: var(--text-color);
}

.message i {
   cursor: pointer;
   color: var(--accent-color);
   font-size: 2.5rem;
}

.message i:hover {
   transform: rotate(90deg);
}

.btn,
.option-btn,
.delete-btn,
.white-btn {
   display: inline-block;
   margin-top: 1rem;
   padding: 1rem 3rem;
   cursor: pointer;
   color: var(--text-color);
   font-size: 1.8rem;
   border-radius: .5rem;
   text-transform: capitalize;
   background-color: var(--primary-color);
   border: 0.1rem solid var(--border-color);
   transition: background-color 0.3s ease, color 0.3s ease;
}

.btn:hover,
.option-btn:hover,
.delete-btn:hover {
   background-color: var(--accent-color);
   color: var(--background-color);
}

.white-btn:hover {
   background-color: var(--background-color);
   color: var(--text-color);
}

@keyframes fadeIn {
   0% {
      transform: translateY(1rem);
      opacity: 0;
   }
   100% {
      transform: translateY(0);
      opacity: 1;
   }
}

.header {
   position: sticky;
   top: 0;
   left: 0;
   right: 0;
   z-index: 1000;
   background-color: var(--primary-color);
   box-shadow: 0 0.5rem 1rem var(--box-shadow-color);
}

.header .flex {
   display: flex;
   align-items: center;
   padding: 2rem;
   justify-content: space-between;
   position: relative;
   max-width: 1200px;
   margin: 0 auto;
}

.header .flex .logo {
   font-size: 2.5rem;
   color: var(--accent-color);
   font-weight: bold;
}

.header .flex .logo span {
   color: var(--accent-color);
}

.header .flex .navbar a {
   margin: 0 1rem;
   font-size: 1.8rem;
   color: var(--text-color);
   transition: color 0.3s ease;
}

.header .flex .navbar a:hover {
   color: var(--accent-color);
}

.header .flex .icons div {
   margin-left: 1.5rem;
   font-size: 2.5rem;
   cursor: pointer;
   color: var(--text-color);
   transition: color 0.3s ease;
}

.header .flex .icons div:hover {
   color: var(--accent-color);
}

.header .flex .account-box {
   position: absolute;
   top: 120%;
   right: 2rem;
   width: 30rem;
   box-shadow: 0 0.5rem 1rem var(--box-shadow-color);
   border-radius: .5rem;
   padding: 2rem;
   text-align: center;
   border-radius: .5rem;
   border: 0.1rem solid var(--border-color);
   background-color: var(--background-color);
   display: none;
   animation: fadeIn .2s linear;
}

.header .flex .account-box.active {
   display: inline-block;
}

.header .flex .account-box p {
   font-size: 2rem;
   color: var(--text-color);
   margin-bottom: 1.5rem;
}

.header .flex .account-box p span {
   color: var(--accent-color);
}

.header .flex .account-box .delete-btn {
   margin-top: 0;
}

.dashboard .box-container {
   display: grid;
   grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
   gap: 1.5rem;
   max-width: 1200px;
   margin: 0 auto;
   align-items: flex-start;
}

.dashboard .box-container .box {
   background-color: var(--primary-color);
   color: var(--text-color);
   border: 0.1rem solid var(--border-color);
   box-shadow: 0 0.5rem 1rem var(--box-shadow-color);
   border-radius: 0.5rem;
   padding: 2rem;
   text-align: center;
   position: relative;
   transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard .box-container .box:hover {
   transform: scale(1.05);
   box-shadow: 0 0.7rem 1.5rem var(--box-shadow-color);
}

.dashboard .box-container .box h3 {
   color: var(--accent-color);
   font-size: 2.5rem;
   margin-bottom: 1rem;
}

.dashboard .box-container .box i {
   font-size: 3rem;
   color: var(--accent-color);
   margin-bottom: 1rem;
}

.dashboard .box-container .box p {
   color: var(--text-color);
   font-size: 1.8rem;
}

/* Example of adding icons */
.dashboard .box-container .box[data-icon="users"]::before {
   content: "\f0c0"; /* FontAwesome Unicode for users */
   font-family: "Font Awesome 5 Free";
   font-weight: 900;
   display: block;
   font-size: 3rem;
   color: var(--accent-color);
   margin-bottom: 1rem;
}

.dashboard .box-container .box[data-icon="orders"]::before {
   content: "\f291"; /* FontAwesome Unicode for clipboard list */
   font-family: "Font Awesome 5 Free";
   font-weight: 900;
   display: block;
   font-size: 3rem;
   color: var(--accent-color);
   margin-bottom: 1rem;
}

.add-products form {
   background-color: var(--primary-color);
   border-radius: .5rem;
   padding: 2rem;
   text-align: center;
   box-shadow: 0 0.5rem 1rem var(--box-shadow-color);
   border: 0.1rem solid var(--border-color);
   max-width: 50rem;
   margin: 0 auto;
}

.add-products form h3 {
   font-size: 2.5rem;
   text-transform: uppercase;
   color: var(--accent-color);
   margin-bottom: 1.5rem;
}

.add-products form .box {
   width: 100%;
   background-color: var(--background-color);
   border-radius: .5rem;
   margin: 1rem 0;
   padding: 1.2rem 1.4rem;
   color: var(--text-color);
   font-size: 1.8rem;
   border: 0.1rem solid var(--border-color);
}

.show-products .box-container {
   display: grid;
   grid-template-columns: repeat(auto-fit, 30rem);
   justify-content: center;
   gap: 1.5rem;
   max-width: 1200px;
   margin: 0 auto;
   align-items: flex-start;
}

.show-products {
   padding-top: 0;
}

.show-products .box-container .box {
   text-align: center;
   padding: 2rem;
   border-radius: .5rem;
   border: 0.1rem solid var(--border-color);
   box-shadow: 0 0.5rem 1rem var(--box-shadow-color);
   background-color: var(--background-color);
}

.show-products .box-container .box img {
   height: 30rem;
   width: 27rem;
}

.show-products .box-container .box .name {
   height: 12rem;
   padding: 1rem 0;
   font-size: 2rem;
   color: var(--text-color);
}

.show-products .box-container .box .price {
   padding: 1rem 0;
   font-size: 2.5rem;
   color: var(--accent-color);
}

.edit-product-form {
   min-height: 100vh;
   background-color: rgba(0, 0, 0, .7);
   display: flex;
   align-items: center;
   justify-content: center;
   padding: 2rem;
   overflow-y: scroll;
   position: fixed;
   top: 0;
   left: 0;
   z-index: 1200;
   width: 100%;
}

.edit-product-form form {
   width: 50rem;
   padding: 2rem;
   text-align: center;
   border-radius: .5rem;
   background-color: var(--background-color);
}

.edit-product-form form img {
   height: 20rem;
   margin-bottom: 1rem;
}

.edit-product-form form .box {
   margin: 1rem 0;
   padding: 1.2rem 1.4rem;
   border: 0.1rem solid var(--border-color);
   border-radius: .5rem;
   background-color: var(--background-color);
   font-size: 1.8rem;
   color: var(--text-color);
   width: 100%;
}

.orders .box-container,
.users .box-container,
.messages .box-container {
   display: grid;
   grid-template-columns: repeat(auto-fit, 30rem);
   justify-content: center;
   gap: 1.5rem;
   max-width: 1200px;
   margin: 0 auto;
   align-items: flex-start;
}

.orders .box-container .box,
.users .box-container .box,
.messages .box-container .box {
   background-color: var(--primary-color);
   padding: 2rem;
   border: 0.1rem solid var(--border-color);
   box-shadow: 0 0.5rem 1rem var(--box-shadow-color);
   border-radius: .5rem;
   text-align: center;
}

.orders .box-container .box p,
.users .box-container .box p,
.messages .box-container .box p {
   padding-bottom: 1.5rem;
   font-size: 2rem;
   color: var(--text-color);
}

.orders .box-container .box p span,
.users .box-container .box p span,
.messages .box-container .box p span {
   color: var(--accent-color);
}

.orders .box-container .box form select,
.users .box-container .box form select {
   border-radius: .5rem;
   margin: .5rem 0;
   width: 100%;
   background-color: var(--background-color);
   border: 0.1rem solid var(--border-color);
   padding: 1.2rem 1.4rem;
   font-size: 1.8rem;
   color: var(--text-color);
}

.delete-btn {
   background-color: var(--primary-color);
}

.white-btn,
.btn {
   background-color: var(--primary-color);
}
