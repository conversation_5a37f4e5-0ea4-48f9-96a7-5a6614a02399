<?php
// Bắt đầu file PHP cho trang chi tiết đơn hàng

// <PERSON><PERSON> gồm controller xử lý logic chi tiết đơn hàng
include('../../controller/orderDetail_ctl.php');
?>
<!DOCTYPE html>
<!-- <PERSON><PERSON> báo loại tài liệu HTML5 -->
<html lang="en">
<!-- B<PERSON>t đầu thẻ HTML với ngôn ngữ tiếng Anh -->

<head>
    <!-- Phần head chứa metadata và các liên kết tài nguyên -->

    <!-- Thiế<PERSON> lập bộ mã ký tự UTF-8 -->
    <meta charset="UTF-8">

    <!-- <PERSON><PERSON><PERSON> b<PERSON><PERSON> tương thích với Internet Explorer -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- Thiết lập viewport cho responsive design -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Ti<PERSON><PERSON> đề trang web -->
    <title>Bookstore</title>

    <!-- <PERSON><PERSON><PERSON> kết đến thư viện CSS MUI từ CDN -->
    <link href="//cdn.muicss.com/mui-0.10.3/css/mui.min.css" rel="stylesheet" type="text/css" />

    <!-- Liên kết đến thư viện JavaScript MUI từ CDN -->
    <script src="//cdn.muicss.com/mui-0.10.3/js/mui.min.js"></script>

    <!-- Liên kết đến thư viện CSS Bootstrap 5.2.3 từ CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">

    <!-- Liên kết đến file CSS tùy chỉnh cho header -->
    <link rel="stylesheet" href="../../../public/css/header.css">

    <!-- Liên kết đến file CSS tùy chỉnh cho footer -->
    <link rel="stylesheet" href="../../../public/css/footer.css">

    <!-- Liên kết đến file CSS tùy chỉnh cho sidebar tài khoản -->
    <link rel="stylesheet" href="../../../public/css/account/sidebar.css">

    <!-- Liên kết đến file CSS tùy chỉnh cho trang chi tiết đơn hàng -->
    <link rel="stylesheet" href="../../../public/css/account/order-detail.css">

    <!-- Liên kết đến thư viện icon FontAwesome -->
    <script src="https://kit.fontawesome.com/9d371022aa.js" crossorigin="anonymous"></script>
</head>

<body>
    <!-- Bắt đầu phần body của trang web -->

    <!-- Bao gồm component header từ file riêng -->
    <?php include '../components/header.php'; ?>

    <!-- Container chính chứa toàn bộ nội dung trang -->
    <div class="main-container">
        <!-- Container fluid Bootstrap cho layout responsive -->
        <div class="container-fluid content">
            <!-- Hàng Bootstrap chia layout thành cột -->
            <div class="row">
                <!-- Cột bên trái chiếm 3/12 độ rộng cho sidebar -->
                <div class="col-3">
                    <!-- Bao gồm component sidebar khách hàng -->
                    <?php include '../components/customer-sidebar.php'; ?>
                </div>

                <!-- Cột bên phải chiếm 9/12 độ rộng cho nội dung chính -->
                <div class="col-9">
                    <!-- Comment: div order-view-detail được comment -->
                    <!-- <div class="order-view-detail"> -->

                    <!-- Phần hiển thị thông tin tổng quan đơn hàng -->
                    <div class="order-view-info">
                        <!-- Tiêu đề trang chi tiết đơn hàng -->
                        <p class="detail-title">CHI TIẾT ĐƒN HÀNG</p>

                        <!-- Hiển thị trạng thái đơn hàng với class CSS động -->
                        <div class="order-view-status <?php echo isset($status_class) ? htmlspecialchars($status_class) : ''; ?>">
                            <?php echo isset($status) ? htmlspecialchars($status) : 'Không xác định'; ?>
                        </div>

                        <!-- Hiển thị mã đơn hàng -->
                        <p> <strong>Mã đơn hàng: </strong><?php echo isset($oder_id) ? htmlspecialchars($oder_id) : 'N/A'; ?></p>

                        <!-- Hiển thị ngày mua -->
                        <p> <strong>Ngày mua: </strong><?php echo isset($oder_date) ? htmlspecialchars($oder_date) : 'N/A'; ?></p>

                        <!-- Hiển thị tổng tiền đơn hàng -->
                        <p><strong>Tổng tiền: </strong><?php echo isset($cost) ? htmlspecialchars($cost) : '0đ'; ?></p>

                        <!-- Kiểm tra nếu đơn hàng có trạng thái = 1 (đang xử lý) thì hiển thị nút hủy -->
                        <?php if(isset($order_status) && $order_status == 1): ?>
                        <div class="mt-3">
                            <!-- Nút hủy đơn hàng với data attribute chứa ID đơn hàng -->
                            <button type="button" id="cancel-order-btn" class="btn btn-danger" data-order-id="<?php echo isset($oder_id) ? htmlspecialchars($oder_id) : ''; ?>">
                                <i class="fas fa-times-circle"></i> Hủy đơn hàng
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>

                    <style>
                        .status-processing {
                            color: #0d6efd;
                            font-weight: 600;
                        }

                        .status-completed {
                            color: #198754;
                            font-weight: 600;
                        }

                        .status-cancelled {
                            color: #dc3545;
                            font-weight: 600;
                        }
                    </style>
                    <div class="container order-view-method">
                        <div class="row">
                            <div class="col">
                                <div class="col-content">
                                    <p class="detail-title">THÔNG TIN NGƯỜI NHẬN</p>
                                    <p><?php echo isset($name) ? htmlspecialchars($name) : 'N/A'; ?></p>
                                    <p><?php echo isset($address) ? htmlspecialchars($address) : 'N/A'; ?></p>
                                    <p><?php echo "TEL: " . (isset($phone) ? htmlspecialchars($phone) : 'N/A'); ?></p>
                                </div>
                            </div>
                            <div class="col">
                                <div class="col-content">
                                    <p class="detail-title">PHƯƠNG THỨC THANH TOÁN</p>
                                    <p><?php echo isset($payment_method) ? htmlspecialchars($payment_method) : 'N/A'; ?></p>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="order-view-product">
                    <p class="detail-title">THÔNG TIN SẢN PHẨM</p>
                    <table class="table">
                        <thead>
                            <tr>
                                <th scope="col">Hình ảnh</th>
                                <th scope="col">Tên</th>
                                <th scope="col">Giá bán</th>
                                <th scope="col">Số lượng</th>
                                <th scope="col">Thành tiền</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if (isset($number_book) && isset($book_info) && isset($book_soluong) && $number_book > 0) {
                                for ($i = 0; $i < $number_book; $i++) {
                                    if (isset($book_info[$i]) && isset($book_soluong[$i])) {
                                        $src = htmlspecialchars($book_info[$i]['cover_image'] ?? '');
                                        $name = htmlspecialchars($book_info[$i]['name'] ?? '');
                                        $price = number_format($book_info[$i]['price'] ?? 0, 0, ',', '.') . "đ";
                                        $cost = number_format(($book_info[$i]['price'] ?? 0) * $book_soluong[$i], 0, ',', '.') . "đ";
                                        echo "
                                                    <tr>
                                                    <td><img class='product-image' src='$src' alt=''></td>
                                                    <td>$name</td>
                                                    <td>$price</td>
                                                    <td>$book_soluong[$i]</td>
                                                    <td>$cost</td>
                                                    </tr>
                                                    ";
                                    }
                                }
                            } else {
                                echo "<tr><td colspan='5' class='text-center'>Không có sản phẩm nào trong đơn hàng này.</td></tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
                <!-- </div> -->
            </div>
        </div>
    </div>
    </div>
    <?php include '../components/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../js/jquery.js"></script>
    <script src="../../js/function.js"></script>

    <script>
        $(document).ready(function() {
            // Xử lý sự kiện khi nhấn nút hủy đơn hàng
            $('#cancel-order-btn').on('click', function() {
                const orderId = $(this).data('order-id');
                const button = $(this);

                if (confirm('Bạn có chắc chắn muốn hủy đơn hàng này?')) {
                    // Gửi yêu cầu AJAX để hủy đơn hàng
                    $.ajax({
                        url: '../../../app/controller/cancel_order_ctl.php',
                        type: 'POST',
                        dataType: 'json',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        data: {
                            order_id: orderId
                        },
                        beforeSend: function() {
                            // Hiển thị trạng thái đang xử lý
                            button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang xử lý...');
                        },
                        success: function(response) {
                            if (response.success) {
                                // Cập nhật giao diện khi hủy đơn hàng thành công
                                $('.order-view-status').removeClass('status-processing').addClass('status-cancelled').text('Đã hủy');
                                button.parent().remove();

                                // Hiển thị thông báo thành công
                                alert(response.message);
                            } else {
                                // Hiển thị thông báo lỗi
                                alert(response.message);
                                button.prop('disabled', false).html('<i class="fas fa-times-circle"></i> Hủy đơn hàng');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('AJAX Error:', status, error);
                            console.log('Response:', xhr.responseText);
                            alert('Đã xảy ra lỗi khi kết nối đến máy chủ: ' + status);
                            button.prop('disabled', false).html('<i class="fas fa-times-circle"></i> Hủy đơn hàng');
                        }
                    });
                }
            });
        });
    </script>
</body>

</html>