<?php
include('../../controller/product_ctl.php');
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="../../../public/css/header.css">
  <link rel="stylesheet" href="../../../public/css/footer.css">
  <link rel="stylesheet" href="../../../public/css/account/account.css">
  <link rel="stylesheet" href="../../../public/css/account/sidebar.css">
  <link rel="stylesheet" href="../../../public/css/home/<USER>">
  <link rel="stylesheet" href="../../../public/css/home/<USER>">
  <link rel="stylesheet" href="../../../public/css/home/<USER>">
  <link rel="stylesheet" href="../../../public/css/mobile.css">
  <link rel="icon" type="image/x-icon" href="../../../public/images/logo/logo.png">
  <script src="https://kit.fontawesome.com/9d371022aa.js" crossorigin="anonymous"></script>
  <!-- SwiperJS CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
  <title>CHILLNFREE BOOKSHOP - Thế giới sách của bạn</title>
</head>

<body>
  <?php include '../components/header.php'; ?>

  <!-- Hero Banner -->
  <div class="hero-banner">
    <div class="hero-overlay"></div>
    <div class="hero-content">
      <h1>Khám phá thế giới qua từng trang sách</h1>
      <p>Tìm kiếm những cuốn sách hay nhất với giá cả phải chăng tại CHILLNFREE BOOKSHOP</p>
      <div class="hero-search">
        <input type="text" placeholder="Tìm kiếm sách yêu thích...">
        <button><i class="fas fa-search"></i></button>
      </div>
    </div>
  </div>

  <!-- Thanh chức năng dưới header -->
  <div class="main-navbar">
    <div class="main-navbar__marquee">
      <marquee behavior="scroll" direction="left" scrollamount="6" class="main-navbar__welcome">Welcome to ChillnFree BookShop - Nơi mang đến cho bạn những cuốn sách hay nhất!</marquee>
    </div>
    <div class="main-navbar__container">
      <a href="home.php" class="main-navbar__btn active"><i class="fa-solid fa-house"></i> Trang Chủ</a>
      <a href="../checkout/cart.php" class="main-navbar__btn"><i class="fa-solid fa-cart-shopping"></i> Giỏ Hàng</a>
      <a href="../checkout/cart.php#checkout" class="main-navbar__btn"><i class="fa-solid fa-credit-card"></i> Thanh Toán</a>
    </div>
  </div>

  <div class="home_container">
    <!-- Category Highlights -->
    <div class="category-highlights">
      <div class="category-card">
        <div class="category-icon">
          <i class="fas fa-book"></i>
        </div>
        <h3>Sách Mới</h3>
      </div>
      <div class="category-card">
        <div class="category-icon">
          <i class="fas fa-fire"></i>
        </div>
        <h3>Bán Chạy</h3>
      </div>
      <div class="category-card">
        <div class="category-icon">
          <i class="fas fa-percent"></i>
        </div>
        <h3>Khuyến Mãi</h3>
      </div>
      <div class="category-card">
        <div class="category-icon">
          <i class="fas fa-star"></i>
        </div>
        <h3>Đánh Giá Cao</h3>
      </div>
    </div>

    <div class="grid">
      <div class="grid_row layout">
        <div class="grid_column-2">
          <nav class="home_category">
            <h4 class="category_heading">
              <i class="fa-solid fa-list"></i>
              Danh mục sách
            </h4>
            <ul class="category-list animate">
              <li class="category-item">
                <a href="home.php?category_id=1" class="category-item__link"><i class="fas fa-book-open"></i> Tiểu thuyết</a>
              </li>
              <li class="category-item">
                <a href="home.php?category_id=2" class="category-item__link"><i class="fas fa-brain"></i> Sách kĩ năng</a>
              </li>
              <li class="category-item">
                <a href="home.php?category_id=3" class="category-item__link"><i class="fas fa-child"></i> Thiếu nhi</a>
              </li>
              <li class="category-item">
                <a href="home.php?category_id=4" class="category-item__link"><i class="fas fa-globe-americas"></i> Sách nước ngoài</a>
              </li>
              <li class="category-item">
                <a href="home.php?category_id=5" class="category-item__link"><i class="fas fa-graduation-cap"></i> Sách giáo khoa</a>
              </li>
              <li class="category-item">
                <a href="home.php?category_id=6" class="category-item__link"><i class="fas fa-book"></i> Sách tham khảo</a>
              </li>
              <li class="category-item">
                <a href="home.php?category_id=7" class="category-item__link"><i class="fas fa-landmark"></i> Sách lịch sử</a>
              </li>
              <li class="category-item">
                <a href="home.php?category_id=8" class="category-item__link"><i class="fas fa-atom"></i> Sách khoa học</a>
              </li>
              <li class="category-item">
                <a href="home.php?category_id=9" class="category-item__link"><i class="fas fa-paint-brush"></i> Sách nghệ thuật</a>
              </li>
              <li class="category-item">
                <a href="home.php?category_id=10" class="category-item__link"><i class="fas fa-chart-line"></i> Sách kinh tế</a>
              </li>
            </ul>

            <div class="category-filter">
              <h5><i class="fas fa-filter"></i> Lọc theo giá</h5>
              <ul class="category-list">
                <li class="category-item">
                  <a href="home.php?price=0-150000" class="category-item__link">
                    <i class="fas fa-tag"></i> 0 - 150.000đ
                  </a>
                </li>
                <li class="category-item">
                  <a href="home.php?price=150000-250000" class="category-item__link">
                    <i class="fas fa-tag"></i> 150.000đ - 250.000đ
                  </a>
                </li>
                <li class="category-item">
                  <a href="home.php?price=250000-350000" class="category-item__link">
                    <i class="fas fa-tag"></i> 250.000đ - 350.000đ
                  </a>
                </li>
                <li class="category-item">
                  <a href="home.php?price=350000-500000" class="category-item__link">
                    <i class="fas fa-tag"></i> 350.000đ - 500.000đ
                  </a>
                </li>
                <li class="category-item">
                  <a href="home.php?price=500000-up" class="category-item__link">
                    <i class="fas fa-tag"></i> Trên 500.000đ
                  </a>
                </li>
              </ul>
            </div>

            <div class="sidebar-promo">
              <div class="promo-card">
                <div class="promo-badge">-30%</div>
                <h5>Ưu đãi đặc biệt</h5>
                <p>Cho đơn hàng đầu tiên</p>
                <button class="promo-btn" id="promo-flash-sale-btn">Xem ngay</button>
              </div>
            </div>
          </nav>
        </div>

        <div class="grid_column-10">
          <div class="home-filter">
            <div class="home-filter__label">
              <span>Sắp xếp theo:</span>
              <div class="filter-buttons">
                <button class="filter-btn active" data-sort="Phổ biến">Phổ biến</button>
                <button class="filter-btn" data-sort="Mới nhất">Mới nhất</button>
                <button class="filter-btn" data-sort="Bán chạy">Bán chạy</button>
                <button class="filter-btn" id="price-filter-btn">
                  Giá <i class="fas fa-chevron-down"></i>
                </button>
              </div>
              <div class="price-dropdown-content">
                <a href="#" data-price-order="asc">Giá: Thấp đến cao</a>
                <a href="#" data-price-order="desc">Giá: Cao đến thấp</a>
              </div>
            </div>
            <div class="home-filter__paginate">
              <ul class="pagination">
                <?php
                $temp = "";
                if (isset($_GET['category_id'])) $temp = "&category_id=" . $_GET['category_id'];
                if ($current_page > 1) {
                  $pre = $current_page - 1;
                  echo "<li class='page-item'><a class='page-link' href='?page=$pre" . $temp . "'><i class='fas fa-chevron-left'></i> Trang Trước</a></li>";
                } else {
                  echo
                  "<li class='page-item disabled'>
                      <a class='page-link' href='#' tabindex='-1' aria-disabled='true'><i class='fas fa-chevron-left'></i> Trang Trước</a>
                    </li>";
                }
                if ($current_page == $total_page && $total_page >= 3) {
                  $last_page_mn3 = ($current_page - 2);
                  echo "<li class='page-item'><a class='page-link' href='?page=$last_page_mn3" . $temp . "'>$last_page_mn3</a></li>";
                }
                for ($i = 1; $i <= $total_page; $i++) {
                  if ($i != $current_page) {
                    if ($i > $current_page - 2 && $i < $current_page + 2) {
                      echo "<li class='page-item'><a class='page-link' href='?page=$i" . $temp . "'>$i</a></li>";
                    }
                  } else {
                    echo "
                      <li class='page-item active' aria-current='page'>
                        <span class='page-link'>$i</span>
                      </li>
                      ";
                  }
                }
                if ($current_page == 1 && $total_page >= 3) {
                  echo "<li class='page-item'><a class='page-link' href='?page=3" . $temp . "'>3</a></li>";
                }
                if ($current_page < $total_page) {
                  $next = $current_page + 1;
                  echo "<li class='page-item'><a class='page-link' href='?page=$next" . $temp . "'>Tiếp Theo <i class='fas fa-chevron-right'></i></a></li>";
                } else {
                  echo
                  "<li class='page-item disabled'>
                     <a class='page-link' href='#' tabindex='-1' aria-disabled='true'>Tiếp Theo <i class='fas fa-chevron-right'></i></a>
                    </li>";
                }
                ?>
              </ul>
            </div>
          </div>

          <div class="home-product">
            <?php
            include('../product/product-list.php');
            ?>
          </div>

          <!-- Wrap Flash Sale include in a div with an ID -->
          <div id="flash-sale-section">
            <?php include '../components/flash_sale.php'; ?>
          </div>

          <div class="featured-products-section">
            <h2 class="featured-title"><i class="fas fa-star"></i> CHILLNFREE BOOKSHOP Đề Xuất</h2>
            <div class="swiper featured-products-swiper">
              <div class="swiper-wrapper featured-products-list">
                <?php
                // Lấy ngẫu nhiên 10 sản phẩm từ bảng book
                $result = mysqli_query($conn, "SELECT * FROM book ORDER BY RAND() LIMIT 10");
                while($row = mysqli_fetch_array($result)) {
                  $price = number_format($row['price'], 0, ',', '.') . "đ";
                  echo "<div class='swiper-slide'>
                    <a href='../product/product-detail.php?book_id={$row['book_id']}' style='text-decoration: none'>
                      <div class='featured-product-card'>
                        <div class='featured-badge'>Đề xuất</div>
                        <img src='{$row['cover_image']}' alt='{$row['name']}' class='featured-product-img'>
                        <div class='featured-product-name'>{$row['name']}</div>
                        <div class='featured-product-price'>{$price}</div>
                        <div class='featured-product-rating'>
                          <i class='fas fa-star'></i>
                          <i class='fas fa-star'></i>
                          <i class='fas fa-star'></i>
                          <i class='fas fa-star'></i>
                          <i class='fas fa-star-half-alt'></i>
                        </div>
                      </div>
                    </a>
                  </div>";
                }
                ?>
              </div>
              <!-- Add navigation -->
              <div class="swiper-button-next"></div>
              <div class="swiper-button-prev"></div>
              <div class="swiper-pagination"></div>
            </div>
          </div>

          <!-- Benefits Section -->
          <div class="benefits-section">
            <div class="benefit-card">
              <div class="benefit-icon">
                <i class="fas fa-truck"></i>
              </div>
              <div class="benefit-content">
                <h3>Giao Hàng Miễn Phí</h3>
                <p>Cho đơn hàng từ 200.000đ</p>
              </div>
            </div>
            <div class="benefit-card">
              <div class="benefit-icon">
                <i class="fas fa-shield-alt"></i>
              </div>
              <div class="benefit-content">
                <h3>Bảo Đảm Chất Lượng</h3>
                <p>Sách chính hãng 100%</p>
              </div>
            </div>
            <div class="benefit-card">
              <div class="benefit-icon">
                <i class="fas fa-exchange-alt"></i>
              </div>
              <div class="benefit-content">
                <h3>Đổi Trả Dễ Dàng</h3>
                <p>Trong vòng 30 ngày</p>
              </div>
            </div>
            <div class="benefit-card">
              <div class="benefit-icon">
                <i class="fas fa-headset"></i>
              </div>
              <div class="benefit-content">
                <h3>Hỗ Trợ 24/7</h3>
                <p>Luôn sẵn sàng giúp đỡ bạn</p>
              </div>
            </div>
          </div>

          <div class="newsletter-section">
            <div class="newsletter-content">
              <h2 class="newsletter-title">Đăng Ký Nhận Thông Báo</h2>
              <p class="newsletter-desc">Nhận thông tin về sách mới và ưu đãi đặc biệt</p>
              <form id="newsletter-form" class="newsletter-form">
                <input type="email" name="newsletter_email" class="newsletter-input" placeholder="Nhập Email Của Bạn" required>
                <button type="submit" class="newsletter-button">Đăng Ký</button>
              </form>
              <div id="newsletter-message" style="margin-top: 15px; display: none; color: white; font-weight: 500; text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <?php
  include '../components/footer.php';
  ?>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous"></script>
  <script src="../../js/jquery.js"></script>
  <script src="../../js/function.js"></script>
  <!-- SwiperJS JS -->
  <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
  <script src="../../js/featured-slider.js"></script>
  <script>
    $(document).ready(function() {
      // Category toggle
      $(".category_heading").on("click", function() {
        $(".category-list").slideToggle(300);
        $(this).find("i").toggleClass("fa-rotate-90");
      });

      // Filter buttons
      $(".filter-btn").on("click", function() {
        // Skip for price filter button which has its own handling
        if ($(this).attr('id') === 'price-filter-btn') {
          return;
        }

        $(".filter-btn").removeClass("active");
        $(this).addClass("active");

        // Get the sort type from data attribute
        const sortType = $(this).data('sort');
        if (sortType) {
          // Build the URL with current parameters
          let currentUrl = new URL(window.location.href);
          let params = new URLSearchParams(currentUrl.search);

          // Update or add the sort parameter
          params.set('sort', sortType);

          // Remove any price_order parameter if it exists
          if (params.has('price_order')) {
            params.delete('price_order');
          }

          // Redirect to the new URL
          window.location.href = `${currentUrl.pathname}?${params.toString()}`;
        }
      });

      // Price filter dropdown toggle
      $("#price-filter-btn").on("click", function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(".price-dropdown-content").toggleClass("active");
      });

      // Price order links
      $(".price-dropdown-content a").on("click", function(e) {
        e.preventDefault();

        // Get the price order from data attribute
        const priceOrder = $(this).data('price-order');

        // Build the URL with current parameters
        let currentUrl = new URL(window.location.href);
        let params = new URLSearchParams(currentUrl.search);

        // Update or add the price_order parameter
        params.set('price_order', priceOrder);

        // Remove any sort parameter if it exists
        if (params.has('sort')) {
          params.delete('sort');
        }

        // Update active button
        $(".filter-btn").removeClass("active");
        $("#price-filter-btn").addClass("active");

        // Redirect to the new URL
        window.location.href = `${currentUrl.pathname}?${params.toString()}`;
      });

      // Close dropdown when clicking outside
      $(document).on("click", function(e) {
        if (!$(e.target).closest("#price-filter-btn").length &&
            !$(e.target).closest(".price-dropdown-content").length) {
          $(".price-dropdown-content").removeClass("active");
        }
      });

      // Set active button based on current URL parameters
      function setActiveFilterButton() {
        let currentUrl = new URL(window.location.href);
        let params = new URLSearchParams(currentUrl.search);

        if (params.has('price_order')) {
          // If price_order parameter exists, set price button as active
          $(".filter-btn").removeClass("active");
          $("#price-filter-btn").addClass("active");
        } else if (params.has('sort')) {
          // If sort parameter exists, find and set the corresponding button as active
          const sortType = params.get('sort');
          $(".filter-btn").removeClass("active");
          $(`.filter-btn[data-sort="${sortType}"]`).addClass("active");
        } else {
          // Default to "Phổ biến" if no parameters
          $(".filter-btn").removeClass("active");
          $(".filter-btn[data-sort='Phổ biến']").addClass("active");
        }
      }

      // Call the function when page loads
      setActiveFilterButton();

      // Initialize Swiper
      var swiper = new Swiper(".featured-products-swiper", {
        slidesPerView: 1,
        spaceBetween: 10,
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        breakpoints: {
          640: {
            slidesPerView: 2,
            spaceBetween: 20,
          },
          768: {
            slidesPerView: 3,
            spaceBetween: 30,
          },
          1024: {
            slidesPerView: 5,
            spaceBetween: 20,
          },
        },
        autoplay: {
          delay: 3000,
          disableOnInteraction: false,
        },
      });
    });

    document.addEventListener('DOMContentLoaded', function() {
      // Add click event listeners to category cards
      document.querySelectorAll('.category-card h3').forEach(function(card) {
        card.addEventListener('click', function() {
          const cardText = this.textContent.trim();
          if (cardText === 'Sách Mới') {
            document.querySelector('.home-product').scrollIntoView({ behavior: 'smooth' });
          } else if (cardText === 'Bán Chạy' || cardText === 'Đánh Giá Cao') {
            document.querySelector('.featured-products-section').scrollIntoView({ behavior: 'smooth' });
          } else if (cardText === 'Khuyến Mãi') { // Add this condition
            document.querySelector('#flash-sale-section').scrollIntoView({ behavior: 'smooth' }); // Target the new ID
          }
        });
      });

      // Add click event listener to the promo button to scroll to flash sale section
      const promoButton = document.getElementById('promo-flash-sale-btn');
      if (promoButton) {
        promoButton.addEventListener('click', function() {
          document.querySelector('#flash-sale-section').scrollIntoView({ behavior: 'smooth' });
        });
      }

      // Newsletter form submission
      const newsletterForm = document.getElementById('newsletter-form');
      const newsletterMessage = document.getElementById('newsletter-message');

      if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
          e.preventDefault();

          const formData = new FormData(newsletterForm);

          $.ajax({
            type: 'POST',
            url: '../../controller/newsletter_ctl.php',
            data: {
              newsletter_email: formData.get('newsletter_email')
            },
            dataType: 'json',
            success: function(response) {
              newsletterMessage.style.display = 'block';

              if (response.success) {
                newsletterMessage.innerHTML = '<div style="background-color: rgba(40, 167, 69, 0.7); padding: 10px; border-radius: 5px;">' + response.message + '</div>';
                newsletterForm.reset();
              } else {
                newsletterMessage.innerHTML = '<div style="background-color: rgba(220, 53, 69, 0.7); padding: 10px; border-radius: 5px;">' + response.message + '</div>';
              }

              // Hide the message after 5 seconds
              setTimeout(function() {
                newsletterMessage.style.display = 'none';
              }, 5000);
            },
            error: function() {
              newsletterMessage.style.display = 'block';
              newsletterMessage.innerHTML = '<div style="background-color: rgba(220, 53, 69, 0.7); padding: 10px; border-radius: 5px;">Đã xảy ra lỗi. Vui lòng thử lại sau.</div>';

              // Hide the message after 5 seconds
              setTimeout(function() {
                newsletterMessage.style.display = 'none';
              }, 5000);
            }
          });
        });
      }
    });
  </script>
</body>

</html>
