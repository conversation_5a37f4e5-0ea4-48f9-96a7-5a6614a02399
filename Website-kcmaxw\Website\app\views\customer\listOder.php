<?php
    while($row = mysqli_fetch_array($db_account_oder_run)){
        if($row['status'] == 1){
            $status = "Đang xử lý";
            $status_class = "status-processing";
        } else if($row['status'] == 2){
            $status = "Hoàn thành";
            $status_class = "status-completed";
        } else if($row['status'] == 3){
            $status = "Đã hủy";
            $status_class = "status-cancelled";
        }

        $cost = number_format($row['cost'], 0, ',', '.') . "đ";
        echo "
        <div class='history-status'>
            <div class='status-count'>
                <p class='status $status_class'>Trạng thái: $status</p>
            </div>
                <table class='table'>
                    <thead>
                        <tr>
                            <th scope='col'>Mã đơn</th>
                            <th scope='col'>Ngày mua</th>
                            <th scope='col'>Tổng tiền</th>
                            <th scope='col'></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>$row[oder_id]</td>
                            <td>$row[oder_date]</td>
                            <td>$cost</td>
                            <td>
                                <div class='action'>
                                    <a href='order-detail.php?oder_id=$row[oder_id]'><button type='button' class='btn btn-primary'>Chi tiết</button></a>";

        // Chỉ hiển thị nút hủy đơn hàng khi đơn hàng đang ở trạng thái "Đang xử lý"
        if($row['status'] == 1) {
            echo "
                                    <button type='button' class='btn btn-danger ms-2 cancel-order-btn' data-order-id='$row[oder_id]'>Hủy đơn</button>";
        }

        echo "
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
        </div>
        ";
    }
?>

<style>
    .status-processing {
        color: #0d6efd;
        font-weight: 600;
    }

    .status-completed {
        color: #198754;
        font-weight: 600;
    }

    .status-cancelled {
        color: #dc3545;
        font-weight: 600;
    }

    .action {
        display: flex;
        gap: 5px;
    }
</style>