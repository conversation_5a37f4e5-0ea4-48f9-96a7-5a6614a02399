<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit9fc7feb51f16e3bffb220757353d9bab
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'P<PERSON><PERSON>ailer\\PHPMailer\\' => 20,
        ),
        'D' => 
        array (
            'Defuse\\Crypto\\' => 14,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
        'Defuse\\Crypto\\' => 
        array (
            0 => __DIR__ . '/..' . '/defuse/php-encryption/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit9fc7feb51f16e3bffb220757353d9bab::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit9fc7feb51f16e3bffb220757353d9bab::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit9fc7feb51f16e3bffb220757353d9bab::$classMap;

        }, null, ClassLoader::class);
    }
}
