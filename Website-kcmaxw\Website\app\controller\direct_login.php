<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

include('../../app/config.php');

// <PERSON><PERSON>m tra kết nối cơ sở dữ liệu
if ($conn->connect_error) {
    die("Kết nối thất bại: " . $conn->connect_error);
}

// Đăng nhập trực tiếp
if (isset($_POST['direct_login'])) {
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    
    // Kiểm tra email
    $sql = "SELECT * FROM customer WHERE email='$email'";
    $result = mysqli_query($conn, $sql);
    
    if (mysqli_num_rows($result) === 1) {
        $row = mysqli_fetch_assoc($result);
        
        // Tạo session và đăng nhập
        $_SESSION['customer_id'] = $row['customer_id'];
        
        // Chuyển hướng đến trang chủ
        header("location: ../customer/home.php");
        exit(0);
    } else {
        echo "<div style='color: red; font-weight: bold;'>Không tìm thấy tài khoản với email: " . $email . "</div>";
    }
}

// Tạo tài khoản mới và đăng nhập
if (isset($_POST['create_and_login'])) {
    $name = mysqli_real_escape_string($conn, $_POST['name']);
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    $password = mysqli_real_escape_string($conn, $_POST['password']);
    $code = md5(rand());
    
    // Mã hóa mật khẩu
    $hashed_password = password_hash($password, PASSWORD_BCRYPT);
    
    // Kiểm tra email đã tồn tại chưa
    $check_sql = "SELECT * FROM customer WHERE email='$email'";
    $check_result = mysqli_query($conn, $check_sql);
    
    if (mysqli_num_rows($check_result) > 0) {
        // Email đã tồn tại, lấy thông tin tài khoản
        $row = mysqli_fetch_assoc($check_result);
        
        // Tạo session và đăng nhập
        $_SESSION['customer_id'] = $row['customer_id'];
        
        // Chuyển hướng đến trang chủ
        header("location: ../customer/home.php");
        exit(0);
    } else {
        // Thêm tài khoản mới
        $insert_sql = "INSERT INTO customer (name, email, pass, code, verify_status) VALUES ('$name', '$email', '$hashed_password', '$code', 1)";
        if (mysqli_query($conn, $insert_sql)) {
            // Lấy ID của tài khoản vừa tạo
            $new_id = mysqli_insert_id($conn);
            
            // Tạo session và đăng nhập
            $_SESSION['customer_id'] = $new_id;
            
            // Chuyển hướng đến trang chủ
            header("location: ../customer/home.php");
            exit(0);
        } else {
            echo "<div style='color: red; font-weight: bold;'>Lỗi khi tạo tài khoản mới: " . mysqli_error($conn) . "</div>";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đăng nhập trực tiếp</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 20px;
        }
        .form-container {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        input[type="text"], input[type="password"], input[type="email"] {
            padding: 8px;
            width: 300px;
            margin-bottom: 10px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h2>Đăng nhập trực tiếp</h2>
    
    <div class="form-container">
        <h3>1. Đăng nhập trực tiếp với email</h3>
        <p>Đăng nhập trực tiếp vào tài khoản bằng email mà không cần mật khẩu.</p>
        <form method="post" action="">
            <div>
                <label for="email">Email:</label><br>
                <input type="email" name="email" value="<EMAIL>" required>
            </div>
            <button type="submit" name="direct_login">Đăng nhập trực tiếp</button>
        </form>
    </div>
    
    <div class="form-container">
        <h3>2. Tạo tài khoản mới và đăng nhập</h3>
        <p>Tạo tài khoản mới và đăng nhập ngay lập tức.</p>
        <form method="post" action="">
            <div>
                <label for="name">Tên:</label><br>
                <input type="text" name="name" value="Nhật Duy" required>
            </div>
            <div>
                <label for="email">Email:</label><br>
                <input type="email" name="email" value="<EMAIL>" required>
            </div>
            <div>
                <label for="password">Mật khẩu:</label><br>
                <input type="password" name="password" value="123456" required>
            </div>
            <button type="submit" name="create_and_login">Tạo tài khoản và đăng nhập</button>
        </form>
    </div>
</body>
</html>
