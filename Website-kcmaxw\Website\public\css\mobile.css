/* mobile.css - Comprehensive mobile styles for all website interfaces */

/* Global Mobile Styles */
@media (max-width: 992px) {
  html {
    font-size: 16px;
  }

  body {
    overflow-x: hidden;
    width: 100%;
  }

  .container, .container-fluid {
    padding-left: 10px;
    padding-right: 10px;
    width: 100%;
    max-width: 100%;
  }

  /* Fix Bootstrap grid issues */
  .row {
    margin-left: -5px;
    margin-right: -5px;
  }

  .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
  .col-sm, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
  .col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
  .col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
    padding-left: 5px;
    padding-right: 5px;
  }

  /* Fix image sizing */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Fix button sizing */
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  /* Fix input sizing */
  input, select, textarea {
    font-size: 16px !important; /* Prevents iOS zoom on focus */
  }
}

/* Small Mobile Devices */
@media (max-width: 576px) {
  /* Header fixes */
  .header {
    padding: 8px 0;
  }

  .header-wrapper {
    width: 100%;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 8px 5px;
  }

  .logo-container {
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
    gap: 10px;
  }

  .logo img {
    width: 50px;
    height: 50px;
    object-fit: contain;
  }

  .site-name {
    font-size: 18px;
    text-align: center;
    margin: 0;
  }

  .menu-search {
    width: 100%;
    margin: 5px 0;
  }

  .menu-search input[type="text"] {
    width: 100%;
    padding: 8px 35px 8px 10px;
  }

  .search-button {
    right: 5px;
  }

  .icons {
    justify-content: center;
    width: 100%;
    gap: 15px;
  }

  /* Main navbar fixes */
  .main-navbar {
    margin-bottom: 0;
    padding: 0.5rem;
    flex-direction: column;
    align-items: center;
  }

  .main-navbar__container {
    margin: 0;
    padding: 5px;
    border-radius: 15px;
    overflow-x: auto;
    justify-content: flex-start;
  }

  .main-navbar__container::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }

  .main-navbar__btn {
    white-space: nowrap;
    padding: 8px 15px;
    font-size: 0.9rem;
  }

  /* Grid system fixes */
  .grid_column-2, .grid_column-10, .grid_column-2-4 {
    width: 100%;
    padding: 0 5px;
  }

  .grid_row {
    margin: 0;
    gap: 15px 0;
  }

  /* Home container fixes */
  .home_container, .grid {
    width: 100%;
    padding: 10px;
    margin-top: 15px;
  }

  /* Product card fixes */
  .home-product-item {
    margin-bottom: 15px;
  }

  /* Table fixes */
  table {
    display: block;
    width: 100%;
    overflow-x: auto;
  }

  table::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }

  th, td {
    white-space: nowrap;
    padding: 8px 10px;
  }

  /* Form fixes */
  form {
    width: 100%;
  }

  .form-group {
    margin-bottom: 15px;
  }

  .form-control {
    padding: 10px;
  }

  /* Account page fixes */
  .profile-content {
    flex-direction: column;
    gap: 15px;
  }

  .profile-info-section, .profile-avatar-section {
    width: 100%;
  }

  /* Sidebar fixes */
  .sidebar {
    width: 100%;
    margin-bottom: 15px;
    padding: 15px;
  }

  /* Fix modal dialogs */
  .modal-dialog {
    margin: 10px;
    width: calc(100% - 20px);
  }

  .modal-content {
    padding: 15px;
  }

  /* Fix footer */
  .footer {
    padding: 20px 10px;
  }

  .footer-content {
    flex-direction: column;
    gap: 20px;
  }

  /* Fix touch targets */
  a, button, .btn, input[type="submit"], input[type="button"] {
    min-height: 44px;
    min-width: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  /* Fix overflow issues */
  .overflow-auto {
    overflow-x: auto;
  }

  .overflow-auto::-webkit-scrollbar {
    display: none;
  }
}

/* Fix for specific components */
@media (max-width: 576px) {
  /* Fix order history tables */
  .history-status table {
    font-size: 0.9rem;
  }

  .history-status .action {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  /* Fix product detail page */
  .product-detail-container {
    flex-direction: column;
  }

  .product-image-container, .product-info-container {
    width: 100%;
  }

  /* Fix product detail layout */
  .product-body .row {
    margin: 0 !important;
  }

  .book-cover {
    padding: 10px !important;
    margin-bottom: 15px !important;
  }

  .book-title-quantity {
    padding: 10px !important;
  }

  .book-image-container img {
    max-width: 100% !important;
    height: auto !important;
  }

  .book-title {
    font-size: 1.5rem !important;
  }

  .book-price {
    font-size: 1.3rem !important;
  }

  .buy-button {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 15px;
  }

  .buy-button button {
    width: 100%;
  }

  /* Fix cart page */
  .cart-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .cart-item-image {
    margin-bottom: 10px;
  }

  .cart-item-price {
    margin-left: 0;
    margin-top: 10px;
  }

  .cart-item-quantity {
    margin-left: 0;
    margin-top: 10px;
  }

  .cart-item-remove {
    position: absolute;
    top: 10px;
    right: 10px;
  }

  /* Fix address book */
  .address-card {
    flex-direction: column;
  }

  .address-actions {
    margin-top: 10px;
    justify-content: flex-start;
  }

  /* Fix login/signup forms */
  .forms {
    max-width: 100% !important;
    width: 100% !important;
  }

  .form {
    padding: 20px !important;
  }

  .input-field {
    margin-bottom: 15px !important;
  }

  /* Fix comment section */
  .comment-card {
    padding: 10px !important;
  }

  .comment-header {
    flex-direction: column !important;
    align-items: flex-start !important;
  }

  .commentor-avatar {
    margin-bottom: 10px !important;
  }

  /* Fix breadcrumb */
  .breadcrumb-container {
    padding: 10px !important;
    margin-bottom: 15px !important;
  }

  /* Fix tabs */
  .nav-tabs {
    flex-wrap: nowrap !important;
    overflow-x: auto !important;
  }

  .nav-tabs::-webkit-scrollbar {
    display: none !important; /* Chrome, Safari, Edge */
  }

  .nav-link {
    white-space: nowrap !important;
    padding: 8px 12px !important;
  }
}
