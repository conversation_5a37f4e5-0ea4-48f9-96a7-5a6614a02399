<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit9ce6b889faeffe24db04db0042bebd45
{
    public static $prefixLengthsPsr4 = array (
        'D' => 
        array (
            'Defuse\\Crypto\\' => 14,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Defuse\\Crypto\\' => 
        array (
            0 => __DIR__ . '/..' . '/defuse/php-encryption/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit9ce6b889faeffe24db04db0042bebd45::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit9ce6b889faeffe24db04db0042bebd45::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit9ce6b889faeffe24db04db0042bebd45::$classMap;

        }, null, ClassLoader::class);
    }
}
