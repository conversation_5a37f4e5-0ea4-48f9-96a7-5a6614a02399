<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Hi<PERSON>n thị thông tin đường dẫn
echo "<h3>Thông tin đường dẫn:</h3>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Script Filename:</strong> " . $_SERVER['SCRIPT_FILENAME'] . "</p>";
echo "<p><strong>PHP Self:</strong> " . $_SERVER['PHP_SELF'] . "</p>";
echo "<p><strong>Request URI:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";

// Hiển thị cấu trúc thư mục
echo "<h3>Cấu trúc thư mục:</h3>";
echo "<pre>";
function listDir($dir, $indent = 0) {
    $files = scandir($dir);
    foreach ($files as $file) {
        if ($file != "." && $file != "..") {
            echo str_repeat("  ", $indent) . "- " . $file;
            if (is_dir($dir . "/" . $file)) {
                echo "/";
            }
            echo "\n";
            if (is_dir($dir . "/" . $file) && $indent < 2) {
                listDir($dir . "/" . $file, $indent + 1);
            }
        }
    }
}
listDir(__DIR__ . "/../..");
echo "</pre>";

// Kiểm tra các đường dẫn
echo "<h3>Kiểm tra đường dẫn:</h3>";
$paths = [
    "../../app/views/LoginAndSignup/login.php",
    "../views/LoginAndSignup/login.php",
    "../../views/LoginAndSignup/login.php",
    "../LoginAndSignup/login.php"
];

foreach ($paths as $path) {
    $fullPath = __DIR__ . "/" . $path;
    $normalizedPath = realpath($fullPath);
    echo "<p><strong>Đường dẫn:</strong> " . $path . "<br>";
    echo "<strong>Đường dẫn đầy đủ:</strong> " . $fullPath . "<br>";
    echo "<strong>Đường dẫn chuẩn hóa:</strong> " . ($normalizedPath ? $normalizedPath : "Không tồn tại") . "<br>";
    echo "<strong>Tồn tại:</strong> " . (file_exists($fullPath) ? "Có" : "Không") . "</p>";
}

// Kiểm tra đường dẫn tương đối từ controller đến login.php
echo "<h3>Đường dẫn từ controller đến login.php:</h3>";
$loginPath = realpath(__DIR__ . "/../views/LoginAndSignup/login.php");
if ($loginPath) {
    echo "<p><strong>Đường dẫn tuyệt đối:</strong> " . $loginPath . "</p>";
    echo "<p><strong>Đường dẫn tương đối từ controller:</strong> ../views/LoginAndSignup/login.php</p>";
} else {
    echo "<p>Không tìm thấy file login.php</p>";
}

// Tạo form để kiểm tra chuyển hướng
echo "<h3>Kiểm tra chuyển hướng:</h3>";
echo "<form method='post' action=''>";
echo "<button type='submit' name='test_redirect_1'>Test: ../../app/views/LoginAndSignup/login.php</button> ";
echo "<button type='submit' name='test_redirect_2'>Test: ../views/LoginAndSignup/login.php</button> ";
echo "<button type='submit' name='test_redirect_3'>Test: ../../views/LoginAndSignup/login.php</button> ";
echo "<button type='submit' name='test_redirect_4'>Test: ../LoginAndSignup/login.php</button>";
echo "</form>";

// Xử lý chuyển hướng
if (isset($_POST['test_redirect_1'])) {
    header("Location: ../../app/views/LoginAndSignup/login.php");
    exit(0);
} elseif (isset($_POST['test_redirect_2'])) {
    header("Location: ../views/LoginAndSignup/login.php");
    exit(0);
} elseif (isset($_POST['test_redirect_3'])) {
    header("Location: ../../views/LoginAndSignup/login.php");
    exit(0);
} elseif (isset($_POST['test_redirect_4'])) {
    header("Location: ../LoginAndSignup/login.php");
    exit(0);
}
?>
