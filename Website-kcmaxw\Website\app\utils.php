<?php
// File chứa các hàm tiện ích cho tính toán khoảng cách và phí vận chuyển

// Hàm tính khoảng cách từ điểm xuất phát đến điểm đến
function calculateDistance($origin, $destination) {
    // Mảng chứa khoảng cách đã định sẵn tính bằng km từ TP.HCM đến các địa điểm khác
    $distances = [
        'Hà Nội' => 1726, // Khoảng cách từ TP.HCM đến Hà Nội
        'KTX khu A dhqq' => 20, // Khoảng cách đến KTX khu A ĐHQG
        'sao kim, hệ mặt trời' => 50000000, // Ví dụ khoảng cách xa (sao Kim)
        'sao thổ, hệ mặt trời' => 1200000000, // Ví dụ khoảng cách rất xa (sao Thổ)
        'TP.HCM' => 0 // Cùng địa điểm (khoảng cách = 0)
    ];

    // Kiểm tra xem điểm đến có tồn tại trong danh sách không và trả về khoảng cách
    // Nếu không tồn tại thì trả về 0
    return isset($distances[$destination]) ? $distances[$destination] : 0;
}

// Hàm tính phí vận chuyển dựa trên điểm đến và trọng lượng
function calculateShippingFee($destination, $weight = 1) {
    // Tham số $weight tính theo kg, giá trị mặc định là 1kg
    // Quy định phí vận chuyển:
    // - Nội thành (TP.HCM, Hà Nội): 25.000đ cho 3kg đầu, +3.000đ/kg tiếp theo
    // - Ngoại tỉnh: 35.000đ cho 3kg đầu, +5.000đ/kg tiếp theo

    // Mảng chứa danh sách các thành phố nội thành
    $city_inners = ['TP.HCM', 'Hà Nội'];

    // Phí cơ bản cho nội thành (25.000đ)
    $base_fee_inner = 25000;

    // Phí cơ bản cho ngoại tỉnh (35.000đ)
    $base_fee_outer = 35000;

    // Phí phụ trội mỗi kg cho nội thành (3.000đ/kg)
    $extra_per_kg_inner = 3000;

    // Phí phụ trội mỗi kg cho ngoại tỉnh (5.000đ/kg)
    $extra_per_kg_outer = 5000;

    // Trọng lượng cơ bản được tính trong phí ban đầu (3kg)
    $base_weight = 3; // kg

    // Kiểm tra xem điểm đến có phải là nội thành không
    if (in_array($destination, $city_inners)) {
        // Nếu là nội thành, áp dụng phí nội thành
        $fee = $base_fee_inner;

        // Nếu trọng lượng vượt quá 3kg cơ bản
        if ($weight > $base_weight) {
            // Tính phí phụ trội: làm tròn lên số kg vượt quá × phí mỗi kg nội thành
            $fee += ceil($weight - $base_weight) * $extra_per_kg_inner;
        }
    } else {
        // Nếu là ngoại tỉnh, áp dụng phí ngoại tỉnh
        $fee = $base_fee_outer;

        // Nếu trọng lượng vượt quá 3kg cơ bản
        if ($weight > $base_weight) {
            // Tính phí phụ trội: làm tròn lên số kg vượt quá × phí mỗi kg ngoại tỉnh
            $fee += ceil($weight - $base_weight) * $extra_per_kg_outer;
        }
    }

    // Trả về tổng phí vận chuyển đã tính
    return $fee;
}
