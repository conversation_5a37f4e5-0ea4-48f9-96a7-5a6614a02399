<?php
include('../../controller/cart_ctl.php');

// T<PERSON>h lại tổng tiền từ các sản phẩm trong giỏ hàng
$total_price = 0;
if(isset($_SESSION['customer_id'])) {
    $customer_id = $_SESSION['customer_id'];
    $db_cart_items = "SELECT * FROM cart WHERE customer_id='$customer_id'";
    $db_cart_items_run = mysqli_query($conn, $db_cart_items);

    while($row = mysqli_fetch_array($db_cart_items_run)) {
        $book_id = $row['book_id'];
        $quantity = $row['quantity'];
        $db_book = "SELECT * FROM book WHERE book_id='$book_id' LIMIT 1";
        $db_book_run = mysqli_query($conn, $db_book);
        $book_info = mysqli_fetch_array($db_book_run);

        $item_total_price = $quantity * $book_info['price'];
        $total_price += $item_total_price;
    }
}
?>
<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thanh toán - ChillnFree BookShop</title>
    <link href="//cdn.muicss.com/mui-0.10.3/css/mui.min.css" rel="stylesheet" type="text/css" />
    <script src="//cdn.muicss.com/mui-0.10.3/js/mui.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
    <link rel="stylesheet" href="../../../public/css/header.css">
    <link rel="stylesheet" href="../../../public/css/footer.css">
    <link rel="stylesheet" href="../../../public/css/checkout/cart.css">
    <link rel="stylesheet" href="../../../public/css/checkout/payment-modern.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
    <script src="https://kit.fontawesome.com/9d371022aa.js" crossorigin="anonymous"></script>
    <style>
        .qr-code-container {
            display: none;
            margin-top: 20px;
            text-align: center;
        }

        .qr-code-container.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        .qr-code-wrapper {
            position: relative;
            width: 280px;
            height: 280px;
            margin: 0 auto 20px;
        }

        .qr-code-wrapper img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 16px;
            border: 1px solid #e2e8f0;
            padding: 12px;
            background-color: white;
            box-shadow: 0 8px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .qr-code-wrapper:hover img {
            transform: scale(1.02);
            box-shadow: 0 12px 25px rgba(0,0,0,0.12);
            border-color: #c9d6e8;
        }

        .qr-logo {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 50px;
            height: 50px;
            background: white;
            border-radius: 50%;
            padding: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }

        .qr-code-wrapper:hover .qr-logo {
            transform: translate(-50%, -50%) scale(1.1);
            box-shadow: 0 6px 12px rgba(0,0,0,0.2);
        }

        .qr-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border: none;
            padding: 0;
            box-shadow: none;
        }

        .qr-code-container h4 {
            color: #4A90E2;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
            font-size: 18px;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        .qr-instructions {
            background-color: #f8f9fa;
            border-radius: 12px;
            padding: 18px;
            margin-top: 20px;
            border-left: 4px solid #4A90E2;
            text-align: left;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .qr-instructions p {
            font-weight: 700;
            color: #4A90E2;
            margin-bottom: 12px;
            font-size: 16px;
        }

        .qr-instructions ol, .qr-instructions ul {
            padding-left: 25px;
            margin-bottom: 12px;
        }

        .qr-instructions li {
            margin-bottom: 8px;
            line-height: 1.5;
            color: #4a5568;
        }

        .bank-details {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px dashed #e2e8f0;
        }

        .bank-details p {
            font-weight: 600;
            color: #4A90E2;
            margin-bottom: 10px;
        }

        .bank-details ul {
            background-color: rgba(74, 144, 226, 0.05);
            padding: 12px 25px;
            border-radius: 8px;
        }

        /* Modern Payment Styles */
        .payment-section, .address-section, .coupon-section {
            margin-bottom: 30px;
        }

        .section-wrapper {
            background-color: #fff;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.05);
            border: 1px solid #edf2f7;
        }

        .form-control {
            background-color: #f9fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #4A90E2;
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
            background-color: #fff;
        }
    </style>
</head>

<body>
    <?php include '../components/header.php'; ?>
    <div class="main-container">
        <div class="cart">
            <form action="../../controller/checkout_ctl.php" method="post">
                <!-- Thanh tiến trình -->
                <div class="progress-container">
                    <div class="progress-steps">
                        <div class="progress-step">
                            <div class="progress-step-number">1</div>
                            <div class="progress-step-label">Giỏ hàng</div>
                        </div>
                        <div class="progress-step active">
                            <div class="progress-step-number">2</div>
                            <div class="progress-step-label">Thanh toán</div>
                        </div>
                        <div class="progress-step">
                            <div class="progress-step-number">3</div>
                            <div class="progress-step-label">Hoàn tất</div>
                        </div>
                    </div>
                </div>

                <style>
                    /* Thêm khoảng cách giữa các nhãn trong thanh tiến trình */
                    .progress-step-label {
                        margin-top: 8px;
                        width: 100%;
                        text-align: center;
                    }
                </style>

                <div class="container">
                    <div class="row">
                        <div class="col-lg-8">
                            <!-- Phương thức thanh toán -->
                            <div class="payment-section mb-4">
                                <h3 class="subtitle">Phương thức thanh toán</h3>
                                <div class="section-wrapper">
                                    <div class="payment-methods">
                                        <div class="payment-method active" data-method="cod">
                                            <input type="radio" name="method-payment" value="Thanh toán khi nhận hàng" class="payment-method-radio" checked>
                                            <div class="payment-method-header">
                                                <img src="https://cdn-icons-png.flaticon.com/512/2331/2331895.png" alt="COD">
                                                <div class="payment-method-title">Thanh toán khi nhận hàng</div>
                                            </div>
                                            <div class="payment-method-description">
                                                Thanh toán bằng tiền mặt khi nhận hàng
                                            </div>
                                        </div>

                                        <div class="payment-method" data-method="momo">
                                            <input type="radio" name="method-payment" value="Ví MoMo" class="payment-method-radio">
                                            <div class="payment-method-header">
                                                <img src="../../../public/images/payment/iconmomo.png" alt="MoMo">
                                                <div class="payment-method-title">Ví MoMo</div>
                                            </div>
                                            <div class="payment-method-description">
                                                Thanh toán qua ví điện tử MoMo
                                            </div>
                                        </div>

                                        <div class="payment-method" data-method="bank">
                                            <input type="radio" name="method-payment" value="Internet Banking" class="payment-method-radio">
                                            <div class="payment-method-header">
                                                <img src="../../../public/images/payment/iconacb.png" alt="Banking">
                                                <div class="payment-method-title">Internet Banking</div>
                                            </div>
                                            <div class="payment-method-description">
                                                Thanh toán qua chuyển khoản ngân hàng
                                            </div>
                                        </div>

                                        <div class="payment-method" data-method="card">
                                            <input type="radio" name="method-payment" value="Thẻ tín dụng/ghi nợ" class="payment-method-radio">
                                            <div class="payment-method-header">
                                                <img src="https://cdn-icons-png.flaticon.com/512/349/349221.png" alt="Credit Card">
                                                <div class="payment-method-title">Thẻ tín dụng/ghi nợ</div>
                                            </div>
                                            <div class="payment-method-description">
                                                Thanh toán qua thẻ Visa, Mastercard, JCB
                                            </div>
                                        </div>
                                    </div>

                                    <!-- QR Code cho MoMo -->
                                    <div class="qr-code-container" id="momo-qr">
                                        <h4><i class="fas fa-qrcode me-2"></i>Quét mã QR để thanh toán</h4>
                                        <div class="qr-code-wrapper">
                                            <img src="../../../public/images/payment/momo.png" alt="MoMo QR Code" id="momo-qr-img">
                                        </div>
                                        <div class="qr-instructions">
                                            <p><i class="fas fa-info-circle me-2"></i>Hướng dẫn thanh toán:</p>
                                            <ol>
                                                <li>Mở ứng dụng MoMo trên điện thoại</li>
                                                <li>Chọn "Quét mã QR"</li>
                                                <li>Quét mã QR ở trên</li>
                                                <li>Xác nhận thanh toán</li>
                                            </ol>
                                        </div>
                                    </div>

                                    <!-- QR Code cho Banking -->
                                    <div class="qr-code-container" id="bank-qr">
                                        <h4><i class="fas fa-university me-2"></i>Quét mã QR để thanh toán qua ngân hàng</h4>
                                        <div class="qr-code-wrapper">
                                            <img src="../../../public/images/payment/acb.png.jpg" alt="Banking QR Code" id="bank-qr-img">
                                        </div>
                                        <div class="qr-instructions">
                                            <p><i class="fas fa-info-circle me-2"></i>Hướng dẫn thanh toán:</p>
                                            <ol>
                                                <li>Mở ứng dụng ngân hàng trên điện thoại</li>
                                                <li>Chọn chức năng "Quét mã QR"</li>
                                                <li>Quét mã QR ở trên</li>
                                                <li>Xác nhận thanh toán</li>
                                            </ol>
                                            <div class="bank-details">
                                                <p><i class="fas fa-university me-2"></i>Thông tin chuyển khoản:</p>
                                                <ul>
                                                    <li><strong>Ngân hàng:</strong> ACB</li>
                                                    <li><strong>Số tài khoản:</strong> 4070351</li>
                                                    <li><strong>Chủ tài khoản:</strong> Phan Nhật Duy</li>
                                                    <li><strong>Nội dung:</strong> Thanh toán đơn hàng #<?= rand(1000000, 9999999) ?></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Địa chỉ nhận hàng -->
                            <div class="address-section mb-4">
                                <h3 class="subtitle">Địa chỉ nhận hàng</h3>
                                <div class="section-wrapper">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="fullname" class="form-label"><i class="fas fa-user me-2"></i>Họ và tên</label>
                                                <input type="text" class="form-control" id="fullname" placeholder="Nhập họ và tên người nhận" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="phone" class="form-label"><i class="fas fa-phone-alt me-2"></i>Số điện thoại</label>
                                                <input type="tel" class="form-control" id="phone" placeholder="Nhập số điện thoại" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="province-select" class="form-label"><i class="fas fa-map-marker-alt me-2"></i>Tỉnh/Thành phố</label>
                                                <select class="form-control" id="province-select" name="province" required>
                                                    <!-- JS sẽ render options ở đây -->
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="district" class="form-label"><i class="fas fa-map me-2"></i>Quận/Huyện</label>
                                                <input type="text" class="form-control" id="district" placeholder="Nhập quận/huyện" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group mb-3">
                                        <label for="address" class="form-label"><i class="fas fa-home me-2"></i>Địa chỉ cụ thể</label>
                                        <textarea class="form-control" id="address" rows="3" placeholder="Nhập địa chỉ cụ thể (số nhà, đường, phường/xã)" required></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Mã giảm giá -->
                            <div class="coupon-section mb-4">
                                <h3 class="subtitle">Mã giảm giá</h3>
                                <div class="section-wrapper">
                                    <div class="coupon-input">
                                        <div class="input-with-icon">
                                            <i class="fas fa-ticket-alt"></i>
                                            <input type="text" placeholder="Nhập mã giảm giá" id="coupon-code">
                                        </div>
                                        <button type="button" id="apply-coupon">
                                            <i class="fas fa-check me-2"></i>Áp dụng
                                        </button>
                                    </div>
                                    <div class="applied-coupons mt-3" id="applied-coupons">
                                        <!-- Mã giảm giá đã áp dụng sẽ hiển thị ở đây -->
                                    </div>
                                    <div class="coupon-suggestions mt-3">
                                        <p><i class="fas fa-gift me-2"></i>Mã giảm giá gợi ý:</p>
                                        <div class="coupon-list">
                                            <div class="coupon-item" onclick="document.getElementById('coupon-code').value='WELCOME10'">
                                                <div class="coupon-code">WELCOME10</div>
                                                <div class="coupon-desc">Giảm 10.000đ cho đơn hàng đầu tiên</div>
                                            </div>
                                            <div class="coupon-item" onclick="document.getElementById('coupon-code').value='FREESHIP'">
                                                <div class="coupon-code">FREESHIP</div>
                                                <div class="coupon-desc">Miễn phí vận chuyển lên đến 25.000đ</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <style>
                                .coupon-input {
                                    display: flex;
                                    gap: 10px;
                                }

                                .input-with-icon {
                                    position: relative;
                                    flex: 1;
                                }

                                .input-with-icon i {
                                    position: absolute;
                                    left: 12px;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    color: #4A90E2;
                                }

                                .input-with-icon input {
                                    width: 100%;
                                    padding: 12px 15px 12px 40px;
                                    border: 1px solid #e1e8ed;
                                    border-radius: 8px;
                                    font-size: 15px;
                                    transition: all 0.3s ease;
                                }

                                .input-with-icon input:focus {
                                    border-color: #4A90E2;
                                    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
                                }

                                .coupon-input button {
                                    padding: 10px 20px;
                                    background-color: #4A90E2;
                                    color: white;
                                    border: none;
                                    border-radius: 8px;
                                    cursor: pointer;
                                    transition: all 0.3s ease;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                }

                                .coupon-input button:hover {
                                    background-color: #357ABD;
                                    transform: translateY(-2px);
                                }

                                .coupon-suggestions {
                                    margin-top: 20px;
                                }

                                .coupon-suggestions p {
                                    font-weight: 600;
                                    color: #4A90E2;
                                    margin-bottom: 10px;
                                }

                                .coupon-list {
                                    display: flex;
                                    flex-wrap: wrap;
                                    gap: 10px;
                                }

                                .coupon-item {
                                    border: 1px dashed #4A90E2;
                                    border-radius: 8px;
                                    padding: 10px 15px;
                                    background-color: rgba(74, 144, 226, 0.05);
                                    cursor: pointer;
                                    transition: all 0.3s ease;
                                    flex: 1;
                                    min-width: 200px;
                                }

                                .coupon-item:hover {
                                    background-color: rgba(74, 144, 226, 0.1);
                                    transform: translateY(-2px);
                                    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.1);
                                }

                                .coupon-code {
                                    font-weight: 700;
                                    color: #4A90E2;
                                    font-size: 16px;
                                    margin-bottom: 5px;
                                }

                                .coupon-desc {
                                    font-size: 13px;
                                    color: #666;
                                }

                                .coupon-tag {
                                    display: inline-flex;
                                    align-items: center;
                                    background-color: #e6f0fb;
                                    color: #4A90E2;
                                    padding: 8px 15px;
                                    border-radius: 20px;
                                    font-size: 14px;
                                    margin-right: 10px;
                                    margin-bottom: 10px;
                                    font-weight: 600;
                                    box-shadow: 0 2px 5px rgba(74, 144, 226, 0.1);
                                }

                                .coupon-tag i {
                                    margin-left: 8px;
                                    cursor: pointer;
                                    transition: all 0.2s ease;
                                }

                                .coupon-tag i:hover {
                                    color: #dc3545;
                                    transform: scale(1.2);
                                }

                                @media (max-width: 768px) {
                                    .coupon-input {
                                        flex-direction: column;
                                    }

                                    .coupon-list {
                                        flex-direction: column;
                                    }
                                }
                            </style>
                        </div>

                        <div class="col-lg-4">
                            <!-- Tổng kết đơn hàng -->
                            <div class="summary">
                                <h3 class="subtitle">Tổng kết đơn hàng</h3>
                                <div class="section-wrapper order-summary-card">
                                    <div class="order-header">
                                        <div class="step-indicator">
                                            <div class="step active">
                                                <span>1</span>
                                                <div class="step-label">Giỏ hàng</div>
                                            </div>
                                            <div class="step active">
                                                <span>2</span>
                                                <div class="step-label">Thanh toán</div>
                                            </div>
                                            <div class="step">
                                                <span>3</span>
                                                <div class="step-label">Hoàn tất</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="order-summary">
                                        <div class="order-summary-grid">
                                            <div class="order-summary-row">
                                                <div class="order-summary-label">
                                                    <i class="fas fa-shopping-basket"></i>
                                                    <span>Tạm tính:</span>
                                                </div>
                                                <div class="order-summary-value">
                                                    <span id="dp-it-price" class="price-value"><?= number_format($total_price, 0, ',', '.') . "đ"; ?></span>
                                                    <input hidden="hidden" type="number" id="it-price" value=<?= $total_price; ?>>
                                                </div>
                                            </div>

                                            <div class="order-summary-row">
                                                <div class="order-summary-label">
                                                    <i class="fas fa-truck"></i>
                                                    <span>Phí vận chuyển:</span>
                                                </div>
                                                <div class="order-summary-value">
                                                    <span id="dp-sv-price" class="price-value">25.000đ</span>
                                                </div>
                                            </div>

                                            <div class="order-summary-row delivery-info">
                                                <div class="delivery-date-container">
                                                    <div class="delivery-icon">
                                                        <i class="fas fa-calendar-alt"></i>
                                                    </div>
                                                    <div class="delivery-details">
                                                        <div class="delivery-label">Dự kiến giao hàng:</div>
                                                        <div class="delivery-estimate">24/5/2025 - 25/5/2025</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="order-summary-row discount-row" id="coupon-discount-container" style="display: none;">
                                            <div class="order-summary-label">
                                                <i class="fas fa-tag"></i>
                                                <span>Giảm giá:</span>
                                            </div>
                                            <div class="order-summary-value">
                                                <span id="coupon-discount" class="price-value discount">0đ</span>
                                                <input type="hidden" name="coupon_code" id="coupon-code-input" value="">
                                                <input type="hidden" name="coupon_discount" id="coupon-discount-input" value="0">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="order-summary-divider"></div>

                                    <div class="order-summary-total">
                                        <div class="total-label">
                                            <i class="fas fa-money-bill-wave"></i>
                                            <span>Tổng cộng:</span>
                                        </div>
                                        <div class="total-value">
                                            <span id="dp-total-price" class="total-price"><?= number_format($total_price + 25000, 0, ',', '.') . "đ"; ?></span>
                                            <input hidden="hidden" type="number" name="all-total-price" id="total-price-input" value=<?= $total_price + 25000; ?>>
                                        </div>
                                    </div>

                                    <div class="payment-actions mt-4">
                                        <a href="cart.php" class="btn btn-back">
                                            <i class="fas fa-arrow-left me-2"></i>Quay lại
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <style>
                                .order-summary-card {
                                    background: linear-gradient(145deg, #f8fbff 0%, #f0f7ff 100%);
                                    border-radius: var(--radius);
                                    box-shadow: 0 10px 25px rgba(74,144,226,0.1);
                                    overflow: hidden;
                                    position: relative;
                                }

                                .order-header {
                                    padding-bottom: 15px;
                                    margin-bottom: 20px;
                                }

                                .step-indicator {
                                    display: flex;
                                    justify-content: space-between;
                                    position: relative;
                                    margin-bottom: 10px;
                                }

                                .step-indicator::before {
                                    content: '';
                                    position: absolute;
                                    top: 15px;
                                    left: 0;
                                    width: 100%;
                                    height: 2px;
                                    background: #e1e8ed;
                                    z-index: 0;
                                }

                                .step {
                                    display: flex;
                                    flex-direction: column;
                                    align-items: center;
                                    position: relative;
                                    z-index: 1;
                                }

                                .step span {
                                    width: 30px;
                                    height: 30px;
                                    border-radius: 50%;
                                    background: #e1e8ed;
                                    color: #666;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-weight: bold;
                                    margin-bottom: 5px;
                                    transition: all 0.3s ease;
                                }

                                .step.active span {
                                    background: #4A90E2;
                                    color: white;
                                    box-shadow: 0 2px 5px rgba(74,144,226,0.3);
                                }

                                .step-label {
                                    font-size: 12px;
                                    color: #666;
                                    font-weight: 500;
                                }

                                .step.active .step-label {
                                    color: #4A90E2;
                                    font-weight: 600;
                                }

                                .order-summary {
                                    padding: 0 0 15px;
                                }

                                .order-summary-grid {
                                    display: flex;
                                    flex-direction: column;
                                    gap: 15px;
                                }

                                .order-summary-row {
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    font-size: 15px;
                                    padding: 5px 0;
                                }

                                .order-summary-label {
                                    display: flex;
                                    align-items: center;
                                    gap: 10px;
                                    color: #4a5568;
                                    font-weight: 500;
                                }

                                .order-summary-label i {
                                    width: 20px;
                                    height: 20px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    color: #4A90E2;
                                    font-size: 14px;
                                }

                                .order-summary-value {
                                    text-align: right;
                                }

                                .price-value {
                                    font-weight: 600;
                                    color: #4a5568;
                                    font-size: 16px;
                                }

                                .discount {
                                    color: #e53935;
                                }

                                .delivery-info {
                                    background-color: #f8f9fa;
                                    border-radius: 8px;
                                    padding: 12px;
                                    margin: 5px 0;
                                    border-left: 3px solid #4A90E2;
                                }

                                .delivery-date-container {
                                    display: flex;
                                    align-items: flex-start;
                                    gap: 12px;
                                    width: 100%;
                                }

                                .delivery-icon {
                                    background-color: rgba(74, 144, 226, 0.1);
                                    width: 32px;
                                    height: 32px;
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    color: #4A90E2;
                                    flex-shrink: 0;
                                }

                                .delivery-details {
                                    flex: 1;
                                }

                                .delivery-label {
                                    font-weight: 500;
                                    color: #4a5568;
                                    margin-bottom: 5px;
                                }

                                .delivery-estimate {
                                    font-weight: 600;
                                    color: #4A90E2;
                                    font-size: 16px;
                                }

                                .order-summary-divider {
                                    height: 1px;
                                    background: linear-gradient(90deg, rgba(74,144,226,0.1) 0%, rgba(74,144,226,0.3) 50%, rgba(74,144,226,0.1) 100%);
                                    margin: 15px 0;
                                }

                                .order-summary-total {
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    padding: 15px 0 5px;
                                    position: relative;
                                }

                                .total-label {
                                    display: flex;
                                    align-items: center;
                                    gap: 10px;
                                    font-size: 18px;
                                    font-weight: 600;
                                    color: #2d3748;
                                }

                                .total-label i {
                                    width: 24px;
                                    height: 24px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    color: #4A90E2;
                                    font-size: 16px;
                                    background-color: rgba(74, 144, 226, 0.1);
                                    border-radius: 50%;
                                }

                                .total-value {
                                    text-align: right;
                                }

                                .total-price {
                                    color: #e53935;
                                    font-size: 24px;
                                    font-weight: 700;
                                    text-shadow: 0 1px 1px rgba(0,0,0,0.05);
                                    background: linear-gradient(90deg, #e53935, #ff6b6b);
                                    -webkit-background-clip: text;
                                    -webkit-text-fill-color: transparent;
                                    background-clip: text;
                                    display: inline-block;
                                }

                                .price-updating {
                                    animation: price-pulse 0.6s ease;
                                }

                                @keyframes price-pulse {
                                    0% {
                                        opacity: 1;
                                        transform: scale(1);
                                    }
                                    50% {
                                        opacity: 0.5;
                                        transform: scale(1.05);
                                    }
                                    100% {
                                        opacity: 1;
                                        transform: scale(1);
                                    }
                                }

                                @media (max-width: 768px) {
                                    .order-summary-card {
                                        padding: 15px;
                                    }

                                    .step span {
                                        width: 25px;
                                        height: 25px;
                                        font-size: 12px;
                                    }

                                    .step-label {
                                        font-size: 10px;
                                    }

                                    .order-summary-total {
                                        font-size: 16px;
                                    }

                                    .total-price {
                                        font-size: 18px;
                                    }
                                }
                            </style>
                        </div>
                    </div>
                </div>

                <div class="row checkout-button">
                    <a href="cart.php" class='btn btn-outline-primary home'>
                        <i class="fas fa-arrow-left me-2"></i>Tiếp tục mua sắm
                    </a>
                    <button type="submit" name="buy" class='btn btn-primary'>
                        <i class="fas fa-credit-card me-2"></i>Tiến hành thanh toán
                    </button>
                </div>

                <style>
                    .checkout-button {
                        display: flex;
                        justify-content: space-between;
                        margin-top: 30px;
                        gap: 15px;
                    }

                    .checkout-button .btn {
                        min-width: 200px;
                        padding: 12px 25px;
                        border-radius: 8px;
                        font-weight: 600;
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    .checkout-button .btn-primary {
                        background: linear-gradient(90deg, #4A90E2 0%, #6C63FF 100%);
                        color: white;
                        border: none;
                        box-shadow: 0 5px 15px rgba(74,144,226,0.3);
                    }

                    .checkout-button .btn-primary:hover {
                        transform: translateY(-3px);
                        box-shadow: 0 8px 20px rgba(74,144,226,0.4);
                    }

                    .checkout-button .btn-outline-primary {
                        background-color: transparent;
                        color: #4A90E2;
                        border: 1px solid #4A90E2;
                    }

                    .checkout-button .btn-outline-primary:hover {
                        background-color: rgba(74, 144, 226, 0.05);
                    }

                    @media (max-width: 768px) {
                        .checkout-button {
                            flex-direction: column;
                        }

                        .checkout-button .btn {
                            width: 100%;
                        }
                    }
                </style>
            </form>
        </div>
    </div>
    <?php include '../components/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous"></script>
    <script src="../../js/jquery.js"></script>
    <script src="../../js/function.js"></script>
    <script src="../../js/provinces.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Render dropdown tỉnh thành
            const provinceSelect = document.getElementById('province-select');
            if (provinceSelect && window.VN_PROVINCES) {
                window.VN_PROVINCES.forEach(function(province) {
                    const opt = document.createElement('option');
                    opt.value = province;
                    opt.textContent = province;
                    provinceSelect.appendChild(opt);
                });
            }
            // Tự động chọn TP.HCM mặc định nếu có
            if (provinceSelect) {
                provinceSelect.value = 'TP.HCM';
            }

            // Xử lý chọn phương thức thanh toán
            const paymentMethods = document.querySelectorAll('.payment-method');
            const momoQR = document.getElementById('momo-qr');
            const bankQR = document.getElementById('bank-qr');

            paymentMethods.forEach(method => {
                method.addEventListener('click', function() {
                    // Bỏ chọn tất cả
                    paymentMethods.forEach(m => {
                        m.classList.remove('active');
                        m.querySelector('input').checked = false;
                    });

                    // Chọn phương thức hiện tại
                    this.classList.add('active');
                    this.querySelector('input').checked = true;

                    // Ẩn tất cả QR
                    momoQR.classList.remove('active');
                    bankQR.classList.remove('active');

                    // Hiển thị QR tương ứng
                    const methodType = this.getAttribute('data-method');
                    if (methodType === 'momo') {
                        momoQR.classList.add('active');
                    } else if (methodType === 'bank') {
                        bankQR.classList.add('active');
                    }
                });
            });

            // Xử lý mã giảm giá
            const applyButton = document.getElementById('apply-coupon');
            const couponInput = document.getElementById('coupon-code');
            const appliedCoupons = document.getElementById('applied-coupons');
            const couponDiscountContainer = document.getElementById('coupon-discount-container');
            const couponDiscount = document.getElementById('coupon-discount');
            const couponCodeInput = document.getElementById('coupon-code-input');
            const couponDiscountInput = document.getElementById('coupon-discount-input');
            const totalPriceInput = document.getElementById('total-price-input');

            applyButton.addEventListener('click', function() {
                const code = couponInput.value.trim();
                if (code) {
                    // Kiểm tra mã giảm giá (giả lập)
                    let discount = 0;
                    let isValid = false;

                    if (code === 'WELCOME10') {
                        discount = 10000;
                        isValid = true;
                    } else if (code === 'FREESHIP') {
                        discount = 25000;
                        isValid = true;
                    } else if (code === 'SALE20') {
                        discount = 20000;
                        isValid = true;
                    } else if (code === 'BOOK10') {
                        // Giảm 10% tổng giá trị đơn hàng
                        const itemPrice = parseInt(document.getElementById('it-price').value, 10);
                        discount = Math.round(itemPrice * 0.1);
                        isValid = true;
                    }

                    if (isValid) {
                        // Xóa tất cả mã giảm giá hiện tại
                        appliedCoupons.innerHTML = '';

                        // Thêm mã giảm giá vào danh sách
                        const couponTag = document.createElement('div');
                        couponTag.className = 'coupon-tag';
                        couponTag.innerHTML = `${code} <i class="fas fa-times-circle"></i>`;
                        appliedCoupons.appendChild(couponTag);

                        // Hiển thị giảm giá
                        couponDiscountContainer.style.display = 'flex';
                        couponDiscount.textContent = `-${discount.toLocaleString('vi-VN')}đ`;

                        // Cập nhật giá trị ẩn cho form
                        couponCodeInput.value = code;
                        couponDiscountInput.value = discount;

                        // Cập nhật tổng tiền
                        updateTotalPrice();

                        // Xóa mã giảm giá khi click vào icon x
                        couponTag.querySelector('i').addEventListener('click', function() {
                            appliedCoupons.removeChild(couponTag);
                            couponDiscountContainer.style.display = 'none';

                            // Xóa giá trị ẩn
                            couponCodeInput.value = '';
                            couponDiscountInput.value = '0';

                            updateTotalPrice();
                        });

                        // Xóa input
                        couponInput.value = '';
                    } else {
                        alert('Mã giảm giá không hợp lệ hoặc đã hết hạn!');
                    }
                }
            });

            // Cập nhật phí vận chuyển và tổng tiền
            function updateTotalPrice() {
                const destination = provinceSelect ? provinceSelect.value : '';
                // Tính phí ship
                const cityInners = ['TP.HCM', 'Hà Nội', 'Đà Nẵng', 'Cần Thơ', 'Hải Phòng'];
                const baseFeeInner = 25000;
                const baseFeeOuter = 35000;
                const extraPerKgInner = 3000;
                const extraPerKgOuter = 5000;
                const baseWeight = 3; // kg
                let weight = 1; // mặc định 1kg
                let shippingFee = 0;

                if (cityInners.includes(destination)) {
                    shippingFee = baseFeeInner;
                    if (weight > baseWeight) {
                        shippingFee += Math.ceil(weight - baseWeight) * extraPerKgInner;
                    }
                } else {
                    shippingFee = baseFeeOuter;
                    if (weight > baseWeight) {
                        shippingFee += Math.ceil(weight - baseWeight) * extraPerKgOuter;
                    }
                }

                // Hiển thị phí vận chuyển với animation
                const serviceFeeElement = document.getElementById('dp-sv-price');
                if (serviceFeeElement) {
                    // Thêm class để tạo hiệu ứng
                    serviceFeeElement.classList.add('price-updating');

                    // Cập nhật giá trị sau 300ms để tạo hiệu ứng
                    setTimeout(() => {
                        serviceFeeElement.textContent = shippingFee.toLocaleString('vi-VN') + 'đ';
                        serviceFeeElement.classList.remove('price-updating');
                    }, 300);
                }

                // Tính tổng tiền
                const itemPrice = parseInt(document.getElementById('it-price').value, 10);
                let totalPrice = itemPrice + shippingFee;

                // Trừ giảm giá nếu có
                let discount = 0;
                if (couponDiscountContainer.style.display !== 'none') {
                    const discountText = couponDiscount.textContent.replace(/[^\d]/g, '');
                    discount = parseInt(discountText, 10) || 0;
                    totalPrice -= discount;

                    // Đảm bảo tổng tiền không âm
                    if (totalPrice < 0) totalPrice = 0;
                }

                // Hiển thị tổng tiền với animation
                const totalPriceElement = document.getElementById('dp-total-price');
                if (totalPriceElement) {
                    // Thêm class để tạo hiệu ứng
                    totalPriceElement.classList.add('price-updating');

                    // Cập nhật giá trị sau 300ms để tạo hiệu ứng
                    setTimeout(() => {
                        totalPriceElement.textContent = totalPrice.toLocaleString('vi-VN') + 'đ';
                        totalPriceElement.classList.remove('price-updating');
                    }, 300);
                }

                // Cập nhật giá trị ẩn cho form
                if (totalPriceInput) {
                    totalPriceInput.value = totalPrice;
                }

                // Hiển thị thời gian giao hàng dự kiến với animation
                const deliveryDates = calculateEstimatedDelivery(destination);
                const deliveryElement = document.querySelector('.delivery-estimate');
                if (deliveryElement) {
                    // Thêm class để tạo hiệu ứng
                    deliveryElement.classList.add('price-updating');

                    // Cập nhật giá trị sau 300ms để tạo hiệu ứng
                    setTimeout(() => {
                        deliveryElement.textContent = `${deliveryDates.min} - ${deliveryDates.max}`;
                        deliveryElement.classList.remove('price-updating');
                    }, 300);
                }
            }

            // Hàm tính thời gian giao hàng dự kiến
            function calculateEstimatedDelivery(province) {
                const cityInners = ['TP.HCM', 'Hà Nội', 'Đà Nẵng', 'Cần Thơ', 'Hải Phòng'];

                const today = new Date();
                let deliveryDate = new Date(today);

                if (cityInners.includes(province)) {
                    // Thành phố lớn: 1-2 ngày
                    deliveryDate.setDate(today.getDate() + 1);
                    const maxDate = new Date(today);
                    maxDate.setDate(today.getDate() + 2);

                    return {
                        min: formatDate(deliveryDate),
                        max: formatDate(maxDate)
                    };
                } else {
                    // Tỉnh khác: 3-5 ngày
                    deliveryDate.setDate(today.getDate() + 3);
                    const maxDate = new Date(today);
                    maxDate.setDate(today.getDate() + 5);

                    return {
                        min: formatDate(deliveryDate),
                        max: formatDate(maxDate)
                    };
                }
            }

            // Hàm định dạng ngày tháng
            function formatDate(date) {
                const day = date.getDate();
                const month = date.getMonth() + 1;
                const year = date.getFullYear();

                return `${day}/${month}/${year}`;
            }

            // Cập nhật giá khi chọn tỉnh/thành phố
            if (provinceSelect) {
                provinceSelect.addEventListener('change', updateTotalPrice);
            }

            // Khởi tạo giá ban đầu
            updateTotalPrice();

            // Xử lý vấn đề hiển thị hình ảnh QR code
            const momoQrImg = document.getElementById('momo-qr-img');
            const bankQrImg = document.getElementById('bank-qr-img');

            // Kiểm tra và xử lý lỗi tải hình ảnh
            function handleImageError(img, fallbackUrl) {
                img.onerror = function() {
                    console.log('Không thể tải hình ảnh từ: ' + img.src);
                    img.src = fallbackUrl;
                    img.onerror = null; // Tránh vòng lặp vô hạn
                };
            }

            // Thiết lập xử lý lỗi cho hình ảnh MoMo
            handleImageError(momoQrImg, 'https://cdn-icons-png.flaticon.com/512/5968/5968299.png');

            // Thiết lập xử lý lỗi cho hình ảnh Banking
            handleImageError(bankQrImg, 'https://cdn-icons-png.flaticon.com/512/2830/2830284.png');
        });
    </script>
</body>
</html>
