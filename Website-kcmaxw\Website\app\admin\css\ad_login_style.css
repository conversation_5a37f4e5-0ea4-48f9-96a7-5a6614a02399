@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
}

body {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f1f6f5;
}

.container {
  position: relative;
  max-width: 430px;
  width: 100%;
  background-color: white;
  border-radius: 30px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.container .forms {
  display: flex;
  align-items: center;
  width: 100%;
  transition: height 0.2s ease;
}

.container .form {
  padding: 30px;
  width: 100%;
}

.container .form .title {
  position: relative;
  font-size: 27px;
  font-weight: 600;
  color: #333;
}

.form .title::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  height: 3px;
  width: 30px;
  background-color: #ADD8E6;  
  border-radius: 25px;
}

.form .input-field {
  position: relative;
  height: 50px;
  width: 100%;
  margin-top: 30px;
}

.input-field input {
  position: absolute;
  padding: 0 35px;
  height: 100%;
  width: 100%;
  border: none;
  outline: none;
  font-size: 16px;
  border-bottom: 2px solid #ccc;
  transition: all 0.2s ease;
}

.input-field input:is(:focus, :valid) {
  border-bottom-color: #ADD8E6;  /* Light blue on focus */
}

.input-field i {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 23px;
  transition: all 0.2s ease;
}

.input-field input:is(:focus, :valid) ~ i {
  color: #ADD8E6;  
}

.input-field i.icon {
  left: 0;
}

.input-field i.showHidePw {
  right: 0;
  padding: 10px;
  cursor: pointer;
}

.input-field i.send {
  right: 0;
  padding: 10px;
  cursor: pointer;
}

.form .checkbox-text {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
  align-items: center;
}

.checkbox-text .checkbox-content {
  display: flex;
  align-items: center;
}

.checkbox-content input {
  accent-color: #ADD8E6;  
  margin: 0px 8px -2px 4px;
}

.form .text,
a {
  color: #333;
  font-size: 14px;
}

.form a {
  color: #ADD8E6;  
  text-decoration: none;
}

.form a:hover {
  text-decoration: underline;
}

.form .button input {
  border: none;
  color: white;
  background-color: darkcyan; 
  letter-spacing: 1px;
  border-radius: 6px;
  font-size: 17px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.button input:hover {
  background-color: rgba(173, 216, 230, 0.6);
}

.form .login-signup {
  margin-top: 30px;
  text-align: center;
}

@media (max-width: 768px) {
  .container {
    width: 90%;
    border-radius: 20px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
  }

  .form .title {
    font-size: 24px;
  }

  .input-field input {
    font-size: 15px;
  }

  .form .button input {
    font-size: 15px;
  }

  .checkbox-text {
    flex-direction: column;
    align-items: flex-start;
  }

  .checkbox-content {
    margin-top: 10px;
  }

  .form .login-signup {
    margin-top: 20px;
  }
}

@media (max-width: 480px) {
  .form .title {
    font-size: 22px;
  }

  .input-field input {
    font-size: 14px;
  }

  .button input {
    font-size: 14px;
    padding: 10px 15px;
  }
}
