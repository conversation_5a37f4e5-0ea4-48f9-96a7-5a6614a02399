<?php
// File model chứa hàm kiểm tra tính hợp lệ của dữ liệu đăng ký

// Hàm kiểm tra tính hợp lệ của thông tin đăng ký
function checkValid($name, $email, $pass, $cpass, $conn){
    // Khởi tạo biến lưu thông báo lỗi
    $mess="";

    // Sử dụng vòng lặp do-while để kiểm tra từng điều kiện
    // (chỉ chạy 1 lần nhưng cho phép dùng break để thoát sớm)
    do{
        // Kiểm tra độ dài tên: phải từ 3 đến 49 ký tự
        if(strlen($name) <= 2 ||strlen($name) >= 50 ){
            $mess = "Hãy điền tên của bạn."; // Thông báo lỗi nếu tên không hợp lệ
            break; // Thoát khỏi vòng lặp
        }

        // Kiểm tra định dạng email bằng filter PHP
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $mess = "Email không hợp lệ."; // Thông báo lỗi nếu email không đúng định dạng
            break; // Thoát khỏi vòng lặp
        }

        // Kiểm tra xem mật khẩu và xác nhận mật khẩu có khớp nhau không
        if(!($pass===$cpass)){
            $mess ="Các mật khẩu đã nhập không khớp. Hãy thử lại."; // Thông báo lỗi nếu mật khẩu không khớp
            break; // Thoát khỏi vòng lặp
        }

        // Kiểm tra xem email đã được đăng ký chưa
        if(mysqli_num_rows(mysqli_query($conn, "SELECT * FROM customer WHERE email='$email'")) > 0){
            $mess ="Email đã được đăng kí"; // Thông báo lỗi nếu email đã tồn tại
            break; // Thoát khỏi vòng lặp
        }

    }while(false); // Vòng lặp chỉ chạy 1 lần

    // Trả về thông báo lỗi (rỗng nếu không có lỗi)
    return $mess;
}
// Kết thúc file PHP
?>