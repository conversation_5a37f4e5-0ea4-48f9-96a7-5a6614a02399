:root {
  --primary-color: #4A90E2;
  --bg-color: #f1f6f5;
  --white: #ffffff;
  --text-color: #333;
  --light-border: #ddd;
  --shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  --radius: 10px;
  --transition: all 0.3s ease-in-out;
}

body {
  background-color: var(--bg-color);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text-color);
}

.content {
  width: 90%;
  max-width: 1200px;
  margin: 30px auto;
}

.main-container {
  background-color: var(--bg-color);
}

.address {
  background-color: var(--white);
  border-radius: var(--radius);
  margin: 15px 0;
  padding: 1.5em;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.address:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.08);
}

.address-table {
  display: flex;
  flex-direction: column;
  row-gap: 25px;
  margin-bottom: 20px;
}

.address-grid {
  border-collapse: separate;
  border-spacing: 15px;
}

.address-row {
  display: flex;
  gap: 20px;
}

.address-cell {
  width: 50%;
  vertical-align: top;
  padding: 0 !important;
}

.address-card {
  background-color: #f9fcff;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.address-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.address-text {
  flex-grow: 1;
  margin-bottom: 15px;
  line-height: 1.5;
  color: #333;
}

.address-actions {
  display: flex;
  justify-content: flex-end;
}

.delete-btn {
  background-color: #dc3545;
  border-color: #dc3545;
  padding: 6px 12px;
  font-size: 14px;
  border-radius: 5px;
}

.delete-btn:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

.add-address-btn {
  background-color: var(--primary-color);
  color: white;
  padding: 8px 20px;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.add-address-btn:hover {
  background-color: #3a7bc8;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.address-title {
  font-weight: 700;
  font-size: 22px;
  color: var(--primary-color);
  margin-bottom: 1em;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 0.7em;
  letter-spacing: 0.3px;
}

.address-subtitle {
  font-weight: 600;
  font-size: 16px;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.address-content {
  font-size: 15px;
  line-height: 1.6;
  color: var(--text-color);
  background-color: #f9fcff;
  padding: 1em;
  border-left: 4px solid var(--primary-color);
  border-radius: 6px;
}

.add-address-section {
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 25px;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;
}

.add-address-section:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.add-address-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.add-icon-container {
  width: 36px;
  height: 36px;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.add-icon-container i {
  color: white;
  font-size: 18px;
}

.add-address-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.add-address-section .form-group {
  margin-bottom: 15px;
}

.add-address-section textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 15px;
  transition: all 0.3s ease;
  resize: vertical;
}

.add-address-section textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.15);
  outline: none;
}

.add-address-actions {
  display: flex;
  justify-content: flex-end;
}

.add-address-submit {
  background-color: var(--primary-color);
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-address-submit:hover {
  background-color: #3a7bc8;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Fix col-3/col-9/row for mobile */
@media (max-width: 768px) {
  .address-grid th:nth-child(2) {
    display: none;
  }

  .address-row {
    flex-direction: column;
    gap: 10px;
  }

  .address-cell {
    width: 100%;
  }

  /* Ẩn ô thứ hai khi hiển thị trên mobile */
  .address-row td:nth-child(2):empty {
    display: none;
  }
}

@media (max-width: 576px) {
  .row {
    flex-direction: column !important;
    gap: 0 !important;
  }

  .col-3, .col-4, .col-9, .col-8, .col-xl-8, .col-xl-4, .col {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box;
  }

  .address-table, .address-title, .address-content {
    padding: 10px !important;
    margin: 0 !important;
  }

  .address-title {
    font-size: 1.1em !important;
  }

  .address-card {
    padding: 12px;
    margin-bottom: 10px;
  }

  .address-text {
    font-size: 14px;
    margin-bottom: 10px;
  }

  .delete-btn {
    padding: 5px 10px;
    font-size: 13px;
  }

  .add-address-section {
    padding: 15px;
    margin-bottom: 15px;
  }

  .add-address-header h3 {
    font-size: 16px;
  }

  .add-icon-container {
    width: 30px;
    height: 30px;
  }

  .add-icon-container i {
    font-size: 16px;
  }

  .add-address-section textarea {
    padding: 10px;
    font-size: 14px;
  }

  .add-address-submit {
    width: 100%;
    padding: 8px 15px;
    font-size: 14px;
  }

  .add-address-actions {
    justify-content: center;
  }
}
