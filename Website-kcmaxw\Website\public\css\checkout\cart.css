:root {
  --primary: #4A90E2;
  --primary-dark: #3a7bc8;
  --secondary: #FF6B6B;
  --accent: #6C63FF;
  --bg-light: #f8fafc;
  --white: #ffffff;
  --text: #333;
  --text-light: #666;
  --border: #e1e8ed;
  --shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  --shadow-hover: 0 15px 35px rgba(0, 0, 0, 0.12);
  --radius: 12px;
  --radius-sm: 8px;
  --transition: all 0.3s ease-in-out;
}

body {
  background: linear-gradient(135deg, var(--bg-light) 0%, #e0e7ff 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text);
}

.main-container {
  background: transparent;
  padding: 30px 0;
}

.cart {
  background-color: var(--white);
  width: 92%;
  max-width: 1200px;
  margin: auto;
  padding: 35px;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: 40px;
  position: relative;
  overflow: hidden;
}

.cart::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary) 0%, var(--accent) 100%);
}

.column-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.subtitle {
  font-size: 22px;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 15px;
}

.checkout-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
}

.checkout-button button {
  background-color: var(--primary);
  color: #fff;
  padding: 12px 0;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition);
  width: 300px;
  max-width: 90vw;
  margin: 0 auto;
  display: block;
}

.checkout-button button:hover {
  background-color: #3a78c5;
}

.section-wrapper {
  box-shadow: var(--shadow);
  padding: 20px;
  border-radius: var(--radius);
  background-color: #fdfefe;
}

.order .section-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.image-info {
  display: flex;
  align-items: center;
  gap: 15px;
  width: 45%;
}

.image-info img {
  object-fit: contain;
  height: 100px;
  width: 100px;
  border-radius: 6px;
  border: 1px solid #ddd;
  background-color: #fff;
}

.item-info {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.5;
  flex: 1;
}

.item-info div {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
}

.item-info-small {
  display: none;
  font-size: 1.2em;
}

.checkout-item {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
  padding: 18px;
  background-color: #f9fafc;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  margin-bottom: 15px;
}

.checkout-item:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-3px);
  border-color: #d1dce7;
}

.checkout-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary) 0%, var(--accent) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.checkout-item:hover::after {
  opacity: 1;
}

.price-quantity {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-shrink: 0;
}

.price-quantity input {
  width: 80px;
  padding: 8px 12px;
  font-size: 16px;
  border: 1px solid #d1dce7;
  border-radius: var(--radius-sm);
  background-color: #fff;
  transition: all 0.2s ease;
  text-align: center;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.price-quantity input:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}

.price-quantity span {
  font-size: 16px;
  color: var(--text);
  font-weight: 500;
}

.price-quantity i.fa-trash-can {
  color: #e53935;
  font-size: 18px;
  cursor: pointer;
  transition: transform 0.2s ease, color 0.2s ease;
}

.price-quantity i.fa-trash-can:hover {
  transform: scale(1.15);
  color: #c62828;
}

.price {
  color: var(--primary);
  font-size: 20px;
  font-weight: 700;
  text-shadow: 0 1px 1px rgba(0,0,0,0.05);
}

.summary .mui-divider {
  margin: 10px 0;
  height: 1px;
  background-color: #ccc;
}

.gogo {
  display: flex;
  justify-content: center;
}

/* --- CUSTOM BEAUTIFY FOR CHECKOUT PAGE --- */
.address .form-group {
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
#province-select {
  width: 100%;
  padding: 10px 12px;
  border-radius: 8px;
  border: 1px solid #cfd8dc;
  font-size: 16px;
  margin-top: 8px;
  background: #f7fafc;
  transition: border 0.2s;
}
#province-select:focus {
  border: 1.5px solid var(--primary);
  outline: none;
  background: #fff;
}

.summary .section-wrapper {
  background: linear-gradient(145deg, #f8fbff 0%, #f0f7ff 100%);
  border: 1.5px solid #e3eaf5;
  box-shadow: 0 10px 25px rgba(74,144,226,0.1);
  border-radius: var(--radius);
  position: relative;
  overflow: hidden;
}

.summary .section-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(108,99,255,0.1) 0%, rgba(255,255,255,0) 70%);
  border-radius: 50%;
  z-index: 0;
}

.summary .section-wrapper > div {
  font-size: 18px;
  padding: 10px 0;
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary .section-wrapper > div:last-child {
  margin-top: 10px;
  padding-top: 15px;
  border-top: 1px dashed rgba(74,144,226,0.2);
}

.summary strong {
  color: #2d3a4a;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.summary #dp-sv-price, .summary #dp-it-price {
  font-weight: 700;
  color: #357ABD;
  font-size: 18px;
}

.summary #dp-total-price {
  font-weight: 700;
  color: #e53935;
  font-size: 22px;
  text-shadow: 0 1px 1px rgba(0,0,0,0.1);
}

.summary .btn-danger {
  width: 100%;
  margin-top: 25px;
  font-size: 18px;
  border-radius: 30px;
  padding: 14px 0;
  background: linear-gradient(90deg, #4A90E2 0%, #6C63FF 100%);
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(74,144,226,0.3);
  position: relative;
  overflow: hidden;
}

.summary .btn-danger:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(74,144,226,0.4);
}

.summary .btn-danger:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(74,144,226,0.3);
}

.summary .btn-danger::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.summary .btn-danger:hover::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(20, 20);
    opacity: 0;
  }
}

.additional .section-wrapper {
  background: linear-gradient(145deg, #f8fbff 0%, #f0f7ff 100%);
  border: 1.5px solid #e3eaf5;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
  justify-content: flex-start;
  border-radius: var(--radius);
  padding: 25px;
  box-shadow: 0 10px 25px rgba(74,144,226,0.08);
  position: relative;
  overflow: hidden;
}

.additional .section-wrapper::before {
  content: '';
  position: absolute;
  bottom: -30px;
  right: -30px;
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, rgba(108,99,255,0.08) 0%, rgba(255,255,255,0) 70%);
  border-radius: 50%;
  z-index: 0;
}

.additional .form-check.form-switch {
  margin-bottom: 0;
  position: relative;
  z-index: 1;
}

.additional .form-check-input {
  width: 3em;
  height: 1.5em;
  margin-top: 0.25em;
  cursor: pointer;
  background-color: rgba(74,144,226,0.2);
  border-color: rgba(74,144,226,0.3);
}

.additional .form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.additional strong {
  color: #4A90E2;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.3px;
}

#book-wrap-quantity {
  width: 70px;
  padding: 8px 12px;
  border-radius: var(--radius-sm);
  border: 1px solid #d1dce7;
  font-size: 16px;
  background-color: #fff;
  margin-left: 8px;
  text-align: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

#book-wrap-quantity:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}

#book-wrap-fee {
  color: #e53935;
  font-weight: 600;
  font-size: 16px;
  text-shadow: 0 1px 1px rgba(0,0,0,0.05);
  background: rgba(229, 57, 53, 0.08);
  padding: 5px 10px;
  border-radius: var(--radius-sm);
}

/* Responsive Styles */
@media (max-width: 576px) {
  .row {
    flex-direction: column !important;
    gap: 0 !important;
  }

  .col-3, .col-4, .col-9, .col-8, .col-xl-8, .col-xl-4, .col {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box;
  }

  .cart {
    width: 95%;
    padding: 20px 15px;
    margin-bottom: 20px;
  }

  .order, .payment, .address, .summary, .additional {
    padding: 15px !important;
    margin: 0 0 15px 0 !important;
  }

  .subtitle {
    font-size: 1.2em !important;
    margin-bottom: 15px;
  }

  .subtitle::after {
    width: 40px;
    height: 2px;
  }

  .checkout-item {
    padding: 15px;
    margin-bottom: 10px;
  }

  .summary .section-wrapper > div {
    font-size: 16px;
    padding: 8px 0;
  }

  .summary #dp-total-price {
    font-size: 20px;
  }

  .summary .btn-danger {
    margin-top: 20px;
    font-size: 16px;
    padding: 12px 0;
  }

  .additional .section-wrapper {
    padding: 20px;
  }

  #book-wrap-fee {
    font-size: 14px;
    padding: 4px 8px;
  }
}

@media (max-width: 768px) {
  .item-info {
    display: none;
  }

  .item-info-small {
    display: block;
    margin: 10px 0;
    padding: 10px;
    background-color: rgba(74,144,226,0.05);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--primary);
  }

  .checkout-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .price-quantity {
    width: 100%;
    justify-content: space-between;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px dashed rgba(74,144,226,0.2);
  }

  .image-info {
    flex-direction: row;
    width: 100%;
    gap: 15px;
  }

  .image-info img {
    width: 80px;
    height: 120px;
  }

  .summary {
    margin-top: 30px;
  }

  .cart-info {
    margin: 0 !important;
  }
}

@media (max-width: 900px) {
  .cart {
    padding: 20px;
  }

  .column-content {
    gap: 20px;
  }

  .section-wrapper {
    padding: 15px;
  }

  .checkout-button button {
    font-size: 16px;
    padding: 12px 0;
  }

  .price {
    font-size: 18px;
  }

  .price-quantity input {
    width: 70px;
    padding: 6px 10px;
  }
}

/* Animation for new items added to cart */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.checkout-item {
  animation: fadeInUp 0.5s ease-out;
}
