<?php
    include('../config.php');
    include('../model/checkLogin_model.php');
    include('../utils.php'); // Include the utility functions
    if(isset($_GET['delete_book']) && isset($_SESSION['customer_id'])){
        $delete_book= "DELETE FROM `cart` WHERE customer_id =$_SESSION[customer_id] AND book_id=$_GET[delete_book]";
        $delete_book_run = mysqli_query($conn,$delete_book);
        if(!$delete_book_run){
            header('location: ../views/LoginAndSignup/login.php');
            exit(0);
        }else{
            header('location: ../views/checkout/cart.php');
            exit(0);
        }
    }
    if(isset($_POST['buy']) && isset($_SESSION['customer_id'])){
        $customer_id = $_SESSION['customer_id'];
        $total_price = $_POST['all-total-price'] ?? 0;
        $payment = mysqli_real_escape_string($conn, $_POST['method-payment'] ?? 'Thanh toán khi nhận hàng');
        $province = $_POST['province'] ?? 'TP.HCM';
        $coupon_code = $_POST['coupon_code'] ?? '';
        $coupon_discount = $_POST['coupon_discount'] ?? 0;

        // Lấy address_id đầu tiên của user (đảm bảo hợp lệ foreign key)
        $address_id = 0;
        $get_address = mysqli_query($conn, "SELECT address_id FROM address WHERE customer_id = $customer_id LIMIT 1");
        if ($row = mysqli_fetch_assoc($get_address)) {
            $address_id = $row['address_id'];
        }

        // Tính phí vận chuyển
        $shipping_fee = calculateShippingFee($province, 1); // 1kg mặc định

        // Áp dụng mã giảm giá nếu có
        if (!empty($coupon_code) && $coupon_discount > 0) {
            $total_price = $total_price - $coupon_discount;
            if ($total_price < 0) $total_price = 0;
        }

        // Cộng phí vận chuyển vào tổng tiền
        $total_price += $shipping_fee;

        $res = mysqli_fetch_assoc(mysqli_query($conn, "SELECT CURDATE() AS date;"));
        $date = $res['date'];

        $order = "INSERT INTO `oder` (`cost`, `oder_date`, `payment_method`, `customer_id`, `address_id`)
                  VALUES ('$total_price', '$date', '$payment','$customer_id','$address_id');";
        $order_run = mysqli_query($conn,$order);
        if(!$order_run){
            header('location: ../views/LoginAndSignup/login.php');
            exit(0);
        }
        // for contain
        $get_oder_id = "SELECT oder_id FROM `oder` ORDER BY oder_id DESC LIMIT 1";
        $get_oder_id_run = mysqli_query($conn,$get_oder_id);
        $oder_id = mysqli_fetch_assoc($get_oder_id_run);
        // UPDATE payment
        $get_cart = "SELECT * FROM `cart` WHERE customer_id=$customer_id";
        $get_cart_run = mysqli_query($conn,$get_cart);
        while($row = mysqli_fetch_array($get_cart_run)){
            $contain = "INSERT INTO contain (oder_id,book_id,quantity)
                        VALUES ($oder_id[oder_id] , $row[book_id], $row[quantity])";
            $contain_run = mysqli_query($conn,$contain);
            if(!$contain_run){
                header('location: ../views/LoginAndSignup/login.php');
                exit(0);
            }
        }
        // Xóa toàn bộ sản phẩm trong giỏ hàng sau khi đặt hàng thành công
        $delete_cart = "DELETE FROM `cart` WHERE customer_id=$customer_id";
        mysqli_query($conn, $delete_cart);
        if($order_run && $get_cart_run){
            $_SESSION['status'] = "Mua hành thành công";
            header('location: ../views/customer/order-history.php');
            exit(0);
        }
    }
?>