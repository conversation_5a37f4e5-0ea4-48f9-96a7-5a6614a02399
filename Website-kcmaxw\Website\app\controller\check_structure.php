<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

include('../../app/config.php');

// <PERSON><PERSON>m tra cấu trúc bảng customer
echo "<h3>Cấu trúc bảng customer:</h3>";
$result = $conn->query("DESCRIBE customer");
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Không thể lấy cấu trúc bảng customer: " . $conn->error . "<br>";
}

// Kiểm tra dữ liệu trong bảng customer
echo "<h3>Dữ liệu trong bảng customer:</h3>";
$result = $conn->query("SELECT customer_id, email, name, LENGTH(pass) as pass_length, verify_status FROM customer");
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Email</th><th>Name</th><th>Password Length</th><th>Verify Status</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['customer_id'] . "</td>";
        echo "<td>" . $row['email'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . $row['pass_length'] . "</td>";
        echo "<td>" . $row['verify_status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Không thể lấy dữ liệu từ bảng customer: " . $conn->error . "<br>";
}
?>
