<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include the config file
include('../config.php');

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "Connected to database successfully<br>";

// Check if the 'oder' table exists
$result = $conn->query("SHOW TABLES LIKE 'oder'");
if ($result->num_rows > 0) {
    echo "Table 'oder' exists<br>";
    
    // Check the structure of the 'oder' table
    $result = $conn->query("DESCRIBE `oder`");
    echo "Structure of 'oder' table:<br>";
    echo "<pre>";
    while ($row = $result->fetch_assoc()) {
        print_r($row);
    }
    echo "</pre>";
    
    // Check some sample data
    $result = $conn->query("SELECT * FROM `oder` LIMIT 5");
    echo "Sample data from 'oder' table:<br>";
    echo "<pre>";
    while ($row = $result->fetch_assoc()) {
        print_r($row);
    }
    echo "</pre>";
} else {
    echo "Table 'oder' does not exist<br>";
}

// Close the connection
$conn->close();
?>
