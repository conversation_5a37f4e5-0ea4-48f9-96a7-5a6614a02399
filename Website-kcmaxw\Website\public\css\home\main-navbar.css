/* <PERSON><PERSON> chức n<PERSON>ng d<PERSON>i header */
.main-navbar {
  width: 100%;
  background: transparent;
  box-shadow: none;
  padding: 0.5rem 0 0.7rem 0;
  margin-bottom: -40px;
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  align-items: center;
}
.main-navbar__container {
  display: flex;
  gap: 14px;
  justify-content: flex-end;
  align-items: center;
  background: linear-gradient(90deg, #aee1f9 0%, #b3dceb 100%);
  border-radius: 32px;
  box-shadow: 0 2px 16px rgba(44, 62, 80, 0.10);
  padding: 7px 28px 7px 28px;
  width: 100%;
  max-width: 100%;
  min-width: 0;
  margin-right: 65px;
  margin-left: 65px;
}
.main-navbar__btn {
  background: #fff;
  color: #222;
  font-family: 'Segoe UI', 'Montserrat', Arial, sans-serif;
  font-weight: 700;
  font-size: 0.92em;
  border: none;
  border-radius: 999px;
  padding: 8px 26px;
  box-shadow: 0 1px 6px rgba(44, 62, 80, 0.07);
  transition: background 0.18s, color 0.18s, box-shadow 0.18s, transform 0.15s;
  text-decoration: none;
  outline: none;
  letter-spacing: 0.5px;
  margin: 0 2px;
  display: inline-block;
  line-height: 1.2;
}
.main-navbar__btn:hover, .main-navbar__btn:focus {
  background: linear-gradient(90deg, #2196f3 0%, #21cbf3 100%);
  color: #fff;
  box-shadow: 0 4px 16px rgba(33, 203, 243, 0.13);
  transform: translateY(-2px) scale(1.04);
}
.main-navbar__marquee {
  flex: 1;
  min-width: 180px;
  max-width: 400px;
  margin-left: 30px;
  margin-right: 18px;
  display: flex;
  align-items: center;
}
.main-navbar__welcome {
  font-family: 'Montserrat', 'Segoe UI', Arial, sans-serif;
  font-size: 1.08em;
  font-weight: 700;
  color: #1976d2;
  background: #e3f2fd;
  border-radius: 16px;
  padding: 6px 18px;
  box-shadow: 0 1px 6px rgba(44, 62, 80, 0.07);
  border: 1.5px solid #b3dceb;
  white-space: nowrap;
  margin-left: 50px;
  margin-right: -75px;
}
@media (max-width: 900px) {
  .main-navbar__container {
    gap: 7px;
    padding: 7px 8px;
    max-width: 100vw;
    margin-right: 0;
  }
  .main-navbar__btn {
    font-size: 0.98em;
    padding: 7px 12px;
  }
  .main-navbar__marquee {
    margin-left: 8px;
    margin-right: 6px;
    min-width: 90px;
    max-width: 180px;
  }
  .main-navbar__welcome {
    font-size: 0.95em;
    padding: 4px 8px;
  }
}
@media (max-width: 600px) {
  .main-navbar {
    flex-direction: column;
    align-items: stretch;
  }
  .main-navbar__marquee {
    margin: 0 0 6px 0;
    justify-content: flex-start;
    max-width: 100vw;
  }
  .main-navbar__btn {
    width: 100%;
    margin: 2px 0;
    text-align: right;
    padding: 8px 0;
  }
}
