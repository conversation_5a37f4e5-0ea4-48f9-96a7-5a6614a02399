language: php
sudo: false

matrix:
  fast_finish: true
  include:
    - dist: precise
      php: "5.3"
      env: USE_PSALM=0
           CHECK_MBSTRING=1
    - dist: precise
      php: "5.4"
      env: USE_PSALM=0
           CHECK_MBSTRING=1
    - dist: precise
      php: "5.5"
      env: USE_PSALM=0
           CHECK_MBSTRING=1
    - dist: precise
      php: "5.6"
      env: USE_PSALM=1
           CHECK_MBSTRING=1
    - php: "7.0"
      env: USE_PSALM=1
           CHECK_MBSTRING=1
    - php: "7.1"
      env: USE_PSALM=1
           CHECK_MBSTRING=1
    - php: "7.2"
      env: USE_PSALM=1
           CHECK_MBSTRING=0
    - php: "7.3"
      env: USE_PSALM=1
           CHECK_MBSTRING=0
    - php: "7.4"
      env: USE_PSALM=1
           CHECK_MBSTRING=0
    - php: "nightly"
      env: USE_PSALM=1
           CHECK_MBSTRING=0
    - php: "hhvm"
      env: USE_PSALM=1
           CHECK_MBSTRING=1
    - php: "master"
      env: USE_PSALM=1
           CHECK_MBSTRING=0
  allow_failures:
    - php: "hhvm"
    - php: "master"

install:
    - composer self-update
    - composer install
    - if [[ $USE_PSALM -eq 1 ]]; then composer require --dev "vimeo/psalm:^0|^1|^2"; fi

script:
    - vendor/bin/phpunit
    - php -dmbstring.func_overload=7 vendor/bin/phpunit
    - if [[ $USE_PSALM -eq 1 ]]; then vendor/bin/psalm; fi
