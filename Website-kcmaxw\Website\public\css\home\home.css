:root {
  --primary-color: #4a90e2;
  --secondary-color: #34a853;
  --accent-color: #fbbc05;
  --danger-color: #ea4335;
  --text-color: #333;
  --light-text: #767676;
  --background: #f8f9fa;
  --card-bg: #ffffff;
  --shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  --border-radius: 12px;
  --transition: all 0.3s ease;
  --font-family: 'Poppins', 'Segoe UI', sans-serif;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--background);
  font-family: var(--font-family);
  color: var(--text-color);
  line-height: 1.6;
}

/* Hero Banner */
.hero-banner {
  background-image: url('../home/<USER>/ari.jpg');
  background-size: cover;
  background-position: center;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  padding: 0 20px;
  position: relative;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1;
}

.hero-content {
  max-width: 800px;
  position: relative;
  z-index: 2;
}

.hero-content h1 {
  font-size: 2.5rem;
  margin-bottom: 15px;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-content p {
  font-size: 1.1rem;
  margin-bottom: 25px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.hero-search {
  display: flex;
  max-width: 500px;
  margin: 0 auto;
}

.hero-search input {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 30px 0 0 30px;
  font-size: 1rem;
  outline: none;
}

.hero-search button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0 25px;
  border-radius: 0 30px 30px 0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.hero-search button:hover {
  background-color: #3a7bc8;
}

/* Category Highlights */
.category-highlights {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin: 30px auto;
  max-width: 1200px;
  padding: 0 15px;
}

.category-card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 20px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.category-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(74, 144, 226, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
}

.category-card i {
  font-size: 24px;
  color: var(--primary-color);
}

.category-card h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

/* Main Navbar */
.main-navbar {
  background-color: var(--card-bg);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.main-navbar__marquee {
  background-color: var(--primary-color);
  color: white;
  padding: 8px 0;
}

.main-navbar__welcome {
  font-weight: 500;
}

.main-navbar__container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  padding: 10px 0;
}

.main-navbar__btn {
  text-decoration: none;
  color: var(--text-color);
  padding: 10px 20px;
  margin: 0 5px;
  border-radius: 30px;
  font-weight: 500;
  transition: all 0.3s;
  display: flex;
  align-items: center;
}

.main-navbar__btn i {
  margin-right: 8px;
}

.main-navbar__btn:hover, .main-navbar__btn.active {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* Home Container */
.home_container {
  width: 90%;
  max-width: 1440px;
  margin: 24px auto;
  padding: 0 15px;
}

.grid {
  margin: 24px auto;
  max-width: 1440px;
  width: 100%;
}

.grid_row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
  gap: 25px 0;
}

.grid_column-2 {
  padding: 0 15px;
  width: 25%;
  transition: var(--transition);
}

.grid_column-2-4 {
  padding: 0 15px 30px 15px;
  width: 20%;
  transition: var(--transition);
}

.grid_column-10 {
  padding: 0 15px;
  width: 75%;
  transition: var(--transition);
}

/* Home Category */
.home_category {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
  margin-bottom: 30px;
}

.category_heading {
  background: linear-gradient(135deg, var(--primary-color), #3a7bc8);
  color: white;
  padding: 18px 20px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.category_heading i {
  margin-right: 10px;
  transition: transform 0.3s;
}

.category-list {
  padding: 15px 0;
  background-color: var(--card-bg);
  transition: var(--transition);
}

.category-item {
  padding: 10px 20px;
  transition: var(--transition);
  list-style: none;
}

.category-item:hover {
  background-color: rgba(74, 144, 226, 0.1);
  transform: translateX(5px);
}

.category-item__link {
  text-decoration: none;
  color: var(--text-color);
  font-size: 15px;
  display: flex;
  align-items: center;
  transition: var(--transition);
}

.category-item__link i {
  margin-right: 10px;
  color: var(--primary-color);
  width: 20px;
  text-align: center;
}

.category-item__link:hover {
  color: var(--primary-color);
}

/* Category Filter */
.category-filter {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  margin-top: 30px;
  margin-bottom: 30px;
}

.category-filter h5 {
  background: linear-gradient(135deg, var(--primary-color), #3a7bc8);
  color: white;
  padding: 18px 20px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
}

.category-filter h5 i {
  margin-right: 10px;
}

.category-filter .category-list {
  padding: 15px 0;
  background-color: var(--card-bg);
}

.category-filter .category-item {
  padding: 10px 20px;
  transition: var(--transition);
}

.category-filter .category-item:hover {
  background-color: rgba(74, 144, 226, 0.1);
  transform: translateX(5px);
}

.category-filter .category-item__link {
  text-decoration: none;
  color: var(--text-color);
  font-size: 15px;
  display: flex;
  align-items: center;
  transition: var(--transition);
}

.category-filter .category-item__link i {
  margin-right: 10px;
  color: var(--primary-color);
  width: 20px;
  text-align: center;
}

.category-filter .category-item__link:hover {
  color: var(--primary-color);
}

/* Sidebar Promo */
.sidebar-promo {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  margin-top: 30px;
  padding: 20px;
}

.promo-card {
  background: linear-gradient(135deg, #ff6b6b, #ee5253);
  border-radius: var(--border-radius);
  padding: 20px;
  text-align: center;
  position: relative;
  color: white;
}

.promo-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: white;
  color: #ee5253;
  font-weight: 700;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 14px;
}

.promo-card h5 {
  font-size: 18px;
  margin-bottom: 10px;
  font-weight: 600;
}

.promo-card p {
  font-size: 14px;
  margin-bottom: 15px;
}

.promo-btn {
  background-color: white;
  color: #ee5253;
  border: none;
  padding: 8px 20px;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.promo-btn:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

/* Home Filter */
.home-filter {
  background: linear-gradient(135deg, var(--primary-color), #3a7bc8);
  border-radius: var(--border-radius);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  color: white;
}

.home-filter__label {
  display: flex;
  align-items: center;
  font-weight: 500;
  position: relative;
  flex-wrap: nowrap;
}

.home-filter__label span {
  margin-right: 15px;
  white-space: nowrap;
}

.filter-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding-bottom: 5px;
  scrollbar-width: thin;
  -ms-overflow-style: none;
}

.filter-buttons::-webkit-scrollbar {
  height: 0;
  width: 0;
  display: none;
}

.filter-btn {
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
  display: flex;
  align-items: center;
  white-space: nowrap;
  flex-shrink: 0;
}

.filter-btn:hover, .filter-btn.active {
  background-color: white;
  color: var(--primary-color);
  transform: translateY(-2px);
}

.filter-btn i {
  margin-left: 5px;
}

/* Price filter dropdown */
.price-dropdown-content {
  display: none;
  position: absolute;
  background-color: white;
  min-width: 180px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  z-index: 10;
  border-radius: 8px;
  top: 100%;
  right: 0;
  margin-top: 5px;
  overflow: hidden;
}

.price-dropdown-content a {
  color: var(--primary-color);
  padding: 12px 16px;
  text-decoration: none;
  display: block;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.price-dropdown-content a:hover {
  background-color: #f1f1f1;
}

.price-dropdown-content.active {
  display: block;
}

.home-filter__paginate {
  display: flex;
  align-items: center;
}

.pagination {
  margin: 0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 5px;
}

.page-item {
  margin: 0 2px;
}

.page-link {
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
}

.page-link:hover {
  background-color: white;
  color: var(--primary-color);
  transform: translateY(-2px);
}

.page-item.active .page-link {
  background-color: white;
  color: var(--primary-color);
  font-weight: 600;
}

.page-item.disabled .page-link {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
  cursor: not-allowed;
}

.page-link i {
  margin: 0 5px;
}

/* Home Product */
.home-product-item {
  position: relative;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e0;
}

.home-product-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.home-product-item__img {
  padding-top: 100%;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  transition: all 0.3s ease;
  background-color: white;
}

.home-product-item:hover .home-product-item__img {
  transform: scale(1.05);
}

.home-product-item__name {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.5;
  min-height: 50px;
  overflow: hidden;
  margin: 15px 15px 10px;
  color: var(--text-color);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  text-align: center;
}

.home-product-item__price {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 15px 15px;
}

.home-product-item__price-old {
  font-size: 14px;
  text-decoration: line-through;
  color: #999;
  margin-right: 8px;
}

.home-product-item__price-new {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-color);
}

.home-product-item__sale-off-percent {
  position: absolute;
  top: 10px;
  right: 10px;
  background: var(--danger-color);
  height: 45px;
  width: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 14px;
  color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Featured Products Section */
.featured-products-section {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  padding: 25px;
  margin-bottom: 30px;
  position: relative;
}

.featured-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.featured-title i {
  color: var(--primary-color);
  margin-right: 10px;
}

.featured-products-swiper {
  width: 100%;
  padding-bottom: 40px;
  position: relative;
}

.featured-products-list {
  width: 100%;
}

.swiper-slide {
  display: flex;
  justify-content: center;
  align-items: stretch;
  height: auto;
}

.featured-product-card {
  background: var(--card-bg);
  border: 1px solid #e0e0e0;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  padding: 20px 15px;
  text-align: center;
  transition: all 0.3s ease;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.featured-product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.featured-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: var(--primary-color);
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 5px 10px;
  border-radius: 20px;
  z-index: 1;
}

.featured-product-img {
  width: 120px;
  height: 160px;
  object-fit: contain;
  margin-bottom: 15px;
  border-radius: 8px;
  transition: transform 0.3s;
}

.featured-product-card:hover .featured-product-img {
  transform: scale(1.05);
}

.featured-product-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 10px;
  min-height: 48px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-clamp: 2;
}

.featured-product-price {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.featured-product-rating {
  color: #ffc107;
  font-size: 14px;
  margin-top: auto;
}

.swiper-button-next, .swiper-button-prev {
  color: var(--text-color) !important;
  background: white;
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.swiper-button-next:after, .swiper-button-prev:after {
  font-size: 18px !important;
  font-weight: bold;
}

.swiper-pagination-bullet-active {
  background-color: var(--primary-color) !important;
}

/* Benefits Section */
.benefits-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.benefit-card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 25px 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s;
}

.benefit-card:hover {
  transform: translateY(-5px);
}

.benefit-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(74, 144, 226, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.benefit-icon i {
  font-size: 24px;
  color: var(--primary-color);
}

.benefit-content h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-color);
}

.benefit-content p {
  font-size: 14px;
  color: var(--light-text);
  margin: 0;
}

/* Newsletter Section */
.newsletter-section {
  background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../home/<USER>/sach.png');
  background-size: cover;
  background-position: center;
  border-radius: var(--border-radius);
  padding: 50px 30px;
  margin-bottom: 30px;
  position: relative;
  overflow: hidden;
}

.newsletter-content {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
  color: white;
}

.newsletter-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 10px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.newsletter-desc {
  font-size: 1.1rem;
  margin-bottom: 25px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.newsletter-form {
  display: flex;
  max-width: 500px;
  margin: 0 auto;
}

.newsletter-input {
  flex: 1;
  padding: 15px 20px;
  border: none;
  border-radius: 30px 0 0 30px;
  font-size: 1rem;
  outline: none;
}

.newsletter-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0 30px;
  border-radius: 0 30px 30px 0;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
}

.newsletter-button:hover {
  background-color: #3a7bc8;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.home-product-item, .category-card, .benefit-card, .featured-product-card {
  animation: fadeIn 0.5s ease;
  animation-fill-mode: both;
}

.grid_column-2-4:nth-child(1) .home-product-item { animation-delay: 0.1s; }
.grid_column-2-4:nth-child(2) .home-product-item { animation-delay: 0.15s; }
.grid_column-2-4:nth-child(3) .home-product-item { animation-delay: 0.2s; }
.grid_column-2-4:nth-child(4) .home-product-item { animation-delay: 0.25s; }
.grid_column-2-4:nth-child(5) .home-product-item { animation-delay: 0.3s; }

.category-card:nth-child(1) { animation-delay: 0.1s; }
.category-card:nth-child(2) { animation-delay: 0.2s; }
.category-card:nth-child(3) { animation-delay: 0.3s; }
.category-card:nth-child(4) { animation-delay: 0.4s; }

.benefit-card:nth-child(1) { animation-delay: 0.1s; }
.benefit-card:nth-child(2) { animation-delay: 0.2s; }
.benefit-card:nth-child(3) { animation-delay: 0.3s; }
.benefit-card:nth-child(4) { animation-delay: 0.4s; }

/* Responsive Styles */
@media (max-width: 1200px) {
  .benefits-section, .category-highlights {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid_column-2-4 {
    width: 25%;
  }
}

@media (max-width: 992px) {
  .grid_column-2 {
    width: 30%;
  }

  .grid_column-10 {
    width: 70%;
  }

  .grid_column-2-4 {
    width: 33.333%;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .newsletter-title {
    font-size: 1.8rem;
  }
}

@media (max-width: 768px) {
  .grid_column-2 {
    width: 100%;
    margin-bottom: 20px;
  }

  .grid_column-10 {
    width: 100%;
  }

  .grid_column-2-4 {
    width: 50%;
  }

  .home-filter {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .hero-content h1 {
    font-size: 1.8rem;
  }

  .newsletter-form {
    flex-direction: column;
  }

  .newsletter-input {
    border-radius: 30px;
    margin-bottom: 15px;
  }

  .newsletter-button {
    border-radius: 30px;
    padding: 15px 30px;
  }

  .benefits-section, .category-highlights {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .grid_column-2-4 {
    width: 100%;
  }

  .home_container, .grid {
    width: 100%;
    padding: 0 10px;
  }

  .hero-content h1 {
    font-size: 1.5rem;
  }

  .hero-content p {
    font-size: 1rem;
  }

  .main-navbar__container {
    overflow-x: auto;
    justify-content: flex-start;
    padding: 10px;
  }

  .main-navbar__btn {
    white-space: nowrap;
  }

  .filter-buttons {
    overflow-x: auto;
    margin-left: 0;
    margin-top: 10px;
    width: 100%;
    padding-bottom: 5px;
  }

  .filter-btn {
    white-space: nowrap;
  }

  .home-filter__label {
    flex-direction: column;
    align-items: flex-start;
  }

  .swiper-button-next, .swiper-button-prev {
    display: none !important;
  }
}