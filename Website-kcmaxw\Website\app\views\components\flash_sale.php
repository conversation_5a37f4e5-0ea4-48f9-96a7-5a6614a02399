<div class="flash-sale">
  <div class="flash-sale-header">
    <h2>FLASH SALE</h2>
    <div class="countdown"><PERSON><PERSON><PERSON> thúc trong <span id="countdown-timer">00:00:00</span></div>
    <a href="#" class="view-all"><PERSON><PERSON> tất cả</a>
  </div>
  <div class="flash-sale-products">
    <div class="product">
      <img src="../../../public/images/dacnhantam.png" alt="Product 1">
      <h3>Đ<PERSON><PERSON> Nhân Tâm</h3>
      <p class="price">63.640đ <span class="original-price">86.000đ</span></p>
      <p class="discount">-26%</p>
    </div>
    <div class="product">
      <img src="../../../public/images/lamthenao.png" alt="Product 2">
      <h3>Làm Thế Nào Để Đắc Nhân Tâm</h3>
      <p class="price">75.000đ <span class="original-price">98.000đ</span></p>
      <p class="discount">-23%</p>
    </div>
    <div class="product">
      <img src="../../../public/images/haisophan.png" alt="Product 3">
      <h3>Hai Số Phận - Bìa Cứng (Tái Bản 2023)</h3>
      <p class="price">185.250đ <span class="original-price">232.000đ</span></p>
      <p class="discount">-22%</p>
    </div>
    <div class="product">
      <img src="../../../public/images/khonggiadinh.png" alt="Product 4">
      <h3>Không Gia Đình - Bìa Cứng (Tái Bản 2024)</h3>
      <p class="price">169.000đ <span class="original-price">215.000đ</span></p>
      <p class="discount">-21%</p>
    </div>
    <div class="product">
      <img src="../../../public/images/thep.png" alt="Product 5">
      <h3>Thép Đã Tôi Thế Đấy (Tái Bản 2023)</h3>
      <p class="price">96.750đ <span class="original-price">125.000đ</span></p>
      <p class="discount">-23%</p>
    </div>
  </div>
</div>
<script>
  function startCountdown(durationInSeconds) {
    const countdownElement = document.getElementById('countdown-timer');
    let remainingTime = durationInSeconds;

    function updateCountdown() {
      const hours = Math.floor(remainingTime / 3600);
      const minutes = Math.floor((remainingTime % 3600) / 60);
      const seconds = remainingTime % 60;

      countdownElement.textContent = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;

      if (remainingTime > 0) {
        remainingTime--;
        setTimeout(updateCountdown, 1000);
      }
    }

    updateCountdown();
  }
  // Start the countdown with a duration of 1 hour (3600 seconds)
  startCountdown(3600);
</script>
