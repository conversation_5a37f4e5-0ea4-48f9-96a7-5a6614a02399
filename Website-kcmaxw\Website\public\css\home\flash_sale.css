.flash-sale {
  background-color: #ff4d4d;
  padding: 20px;
  border-radius: 10px;
  margin: 20px 0;
  color: #fff;
}

.flash-sale-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  margin-top: 10px;
  flex-wrap: wrap;
  gap: 10px;
}
.flash-sale-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.flash-sale-header h2 {
  color: white;
  font-size: 24px;
  font-weight: bold;
  display: flex;
  align-items: center;
  margin: 0;
}

.flash-sale-header h2::before {
  content: '\26A1';
  color: #fff;
  font-size: 24px;
  margin-right: 5px;
}

.flash-sale-header .countdown {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  color: white;
}

.flash-sale-header .countdown span {
  background-color: #000;
  color: #fff;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 5px;
}

.flash-sale-header .view-all {
  color: #ff4d4d;
  text-decoration: none;
  font-weight: bold;
  background-color: #000;
  padding: 5px 10px;
  border-radius: 5px;
}
/* Product Carousel */
.flash-sale-products {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  padding-bottom: 10px;
}

.flash-sale-products::-webkit-scrollbar {
  height: 8px;
}

.flash-sale-products::-webkit-scrollbar-thumb {
  background-color: #fff;
  border-radius: 10px;
}

.flash-sale-products::-webkit-scrollbar-track {
  background-color: #ff4d4d;
  border-radius: 10px;
}
/* Product Card */
.product {
  background-color: #fff;
  border: none;
  border-radius: 10px;
  padding: 10px;
  text-align: center;
  width: 200px;
  flex-shrink: 0;
  color: #333;
}

.product img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 10px;
  margin-bottom: 10px;
}

.product h3 {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  box-orient: vertical;
}

.product .price {
  font-size: 16px;
  color: #ff4d4d;
  font-weight: bold;
}

.product .original-price {
  text-decoration: line-through;
  color: #999;
  font-size: 12px;
  margin-left: 5px;
}

.product .discount {
  background-color: #ff4d4d;
  color: #fff;
  font-size: 12px;
  padding: 2px 5px;
  border-radius: 5px;
  display: inline-block;
  margin-top: 5px;
}
