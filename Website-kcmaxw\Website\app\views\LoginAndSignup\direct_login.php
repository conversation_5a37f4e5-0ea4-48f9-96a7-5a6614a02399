<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

include('../../../app/config.php');

// Kiểm tra nếu đã đăng nhập
if(isset($_SESSION['customer_id'])){
    header('location: ../../customer/home.php');
    exit(0);
}

// Xử lý đăng nhập trực tiếp
if(isset($_POST['direct_login'])){
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    
    // Kiểm tra email
    $sql = "SELECT * FROM customer WHERE email='$email'";
    $result = mysqli_query($conn, $sql);
    
    if(mysqli_num_rows($result) === 1){
        $row = mysqli_fetch_assoc($result);
        
        // Tạo session và đăng nhập
        $_SESSION['customer_id'] = $row['customer_id'];
        
        // Chuyển hướng đến trang chủ
        header("location: ../../customer/home.php");
        exit(0);
    }else{
        $error = "Không tìm thấy tài khoản với email này.";
    }
}

// Đặt lại mật khẩu
if(isset($_POST['reset_password'])){
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    $new_password = "123456"; // Mật khẩu mặc định
    
    // Kiểm tra email
    $sql = "SELECT * FROM customer WHERE email='$email'";
    $result = mysqli_query($conn, $sql);
    
    if(mysqli_num_rows($result) === 1){
        // Cập nhật mật khẩu (không mã hóa)
        $update_sql = "UPDATE customer SET pass='$new_password', verify_status=1 WHERE email='$email'";
        if(mysqli_query($conn, $update_sql)){
            $success = "Đặt lại mật khẩu thành công! Mật khẩu mới là: 123456";
        }else{
            $error = "Lỗi khi đặt lại mật khẩu: " . mysqli_error($conn);
        }
    }else{
        $error = "Không tìm thấy tài khoản với email này.";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Iconscout  -->
    <link
      rel="stylesheet"
      href="https://unicons.iconscout.com/release/v4.0.0/css/line.css"
    />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/css/bootstrap.min.css" 
    integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" 
    crossorigin="anonymous">
    <!-- Css style -->
    <link rel="stylesheet" href="../../../public/css/LoginAndSignup/style.css" />

    <title>Đăng nhập trực tiếp</title>
    <style>
        .tab-buttons {
            display: flex;
            margin-bottom: 20px;
        }
        .tab-button {
            flex: 1;
            padding: 10px;
            text-align: center;
            background-color: #f0f0f0;
            cursor: pointer;
            border: 1px solid #ddd;
        }
        .tab-button.active {
            background-color: #fff;
            border-bottom: none;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .alert {
            margin-top: 10px;
        }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="forms">
        <div class="form login">
          <span class="title">Đăng nhập trực tiếp</span>
          
          <div class="tab-buttons">
            <div class="tab-button active" onclick="showTab('direct-login')">Đăng nhập trực tiếp</div>
            <div class="tab-button" onclick="showTab('reset-password')">Đặt lại mật khẩu</div>
          </div>
          
          <?php if(isset($error)): ?>
            <div class="alert alert-danger" role="alert">
                <?php echo $error; ?>
            </div>
          <?php endif; ?>
          
          <?php if(isset($success)): ?>
            <div class="alert alert-success" role="alert">
                <?php echo $success; ?>
            </div>
          <?php endif; ?>
          
          <!-- Tab đăng nhập trực tiếp -->
          <div id="direct-login" class="tab-content active">
              <form action="" method="post">
                <div class="input-field">
                  <input
                    type="text"
                    name="email"
                    value="<EMAIL>"
                    placeholder="Nhập địa chỉ email"
                    required
                  />
                  <i class="uil uil-envelope icon"></i>
                </div>
                
                <div class="input-field button">
                  <input type="submit" value="Đăng nhập trực tiếp" name="direct_login" />
                </div>
              </form>
          </div>
          
          <!-- Tab đặt lại mật khẩu -->
          <div id="reset-password" class="tab-content">
              <form action="" method="post">
                <div class="input-field">
                  <input
                    type="text"
                    name="email"
                    value="<EMAIL>"
                    placeholder="Nhập địa chỉ email"
                    required
                  />
                  <i class="uil uil-envelope icon"></i>
                </div>
                
                <div class="input-field button">
                  <input type="submit" value="Đặt lại mật khẩu" name="reset_password" />
                </div>
              </form>
          </div>

          <div class="login-signup">
            <span class="text"
              >Quay lại trang đăng nhập thường?
              <a href="login.php">Đăng nhập</a>
            </span>
          </div>
          
          <div class="input-field button" style="margin-top: 20px;">
            <input type="button" onclick="location.href='../customer/home.php';" value="Trang chủ" />
          </div>
        </div>
      </div>
    </div>

    <script>
        // JavaScript để chuyển tab
        function showTab(tabId) {
            // Ẩn tất cả các tab
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Hiển thị tab được chọn
            document.getElementById(tabId).classList.add('active');
            
            // Cập nhật trạng thái active của các nút tab
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Tìm nút tab tương ứng và đánh dấu là active
            document.querySelectorAll('.tab-button').forEach(button => {
                if (button.textContent.toLowerCase().includes(tabId.replace('-', ' '))) {
                    button.classList.add('active');
                }
            });
        }
    </script>
  </body>
</html>
