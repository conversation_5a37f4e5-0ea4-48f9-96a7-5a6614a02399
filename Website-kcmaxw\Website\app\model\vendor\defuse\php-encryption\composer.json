{"name": "defuse/php-encryption", "description": "Secure PHP Encryption Library", "license": "MIT", "keywords": ["security", "encryption", "AES", "openssl", "cipher", "cryptography", "symmetric key cryptography", "crypto", "encrypt", "authenticated encryption"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://defuse.ca/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "autoload": {"psr-4": {"Defuse\\Crypto\\": "src"}}, "require": {"paragonie/random_compat": ">= 2", "ext-openssl": "*", "php": ">=5.6.0"}, "require-dev": {"yoast/phpunit-polyfills": "^2.0.0", "phpunit/phpunit": "^5|^6|^7|^8|^9|^10"}, "bin": ["bin/generate-defuse-key"]}