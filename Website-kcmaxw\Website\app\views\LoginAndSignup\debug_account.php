<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

include('../../../app/config.php');

// <PERSON>ểm tra kết nối cơ sở dữ liệu
echo "<h3><PERSON>ể<PERSON> tra kết nối cơ sở dữ liệu:</h3>";
if ($conn->connect_error) {
    die("Kết nối thất bại: " . $conn->connect_error);
} else {
    echo "Kết nối thành công đến cơ sở dữ liệu: " . $database . "<br>";
}

// Kiểm tra thông tin tài khoản
if(isset($_POST['check_account'])){
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    
    // Kiểm tra email
    $sql = "SELECT * FROM customer WHERE email='$email'";
    $result = mysqli_query($conn, $sql);
    
    if(mysqli_num_rows($result) === 1){
        $row = mysqli_fetch_assoc($result);
        
        echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>Thông tin tài khoản:</h4>";
        echo "<p><strong>ID:</strong> " . $row['customer_id'] . "</p>";
        echo "<p><strong>Email:</strong> " . $row['email'] . "</p>";
        echo "<p><strong>Tên:</strong> " . $row['name'] . "</p>";
        echo "<p><strong>Mật khẩu:</strong> " . htmlspecialchars($row['pass']) . "</p>";
        echo "<p><strong>Độ dài mật khẩu:</strong> " . strlen($row['pass']) . " ký tự</p>";
        echo "<p><strong>Trạng thái xác thực:</strong> " . ($row['verify_status'] == 1 ? 'Đã xác thực' : 'Chưa xác thực') . "</p>";
        echo "</div>";
        
        // Kiểm tra mật khẩu mẫu
        $test_passwords = ["123456", "password", "123", $row['email']];
        
        echo "<div style='background-color: #e9f7ef; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>Kiểm tra mật khẩu:</h4>";
        
        foreach($test_passwords as $test_pass){
            echo "<p><strong>Mật khẩu thử:</strong> " . $test_pass . "</p>";
            echo "<p><strong>So sánh trực tiếp:</strong> " . ($test_pass === $row['pass'] ? 'Khớp' : 'Không khớp') . "</p>";
            echo "<p><strong>Kiểm tra với password_verify:</strong> " . (password_verify($test_pass, $row['pass']) ? 'Khớp' : 'Không khớp') . "</p>";
            echo "<hr>";
        }
        
        echo "</div>";
    }else{
        echo "<div style='color: red; font-weight: bold;'>Không tìm thấy tài khoản với email: " . $email . "</div>";
    }
}

// Đặt lại mật khẩu
if(isset($_POST['reset_password'])){
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    $new_password = mysqli_real_escape_string($conn, $_POST['new_password']);
    $hash_password = isset($_POST['hash_password']) ? true : false;
    
    // Kiểm tra email
    $sql = "SELECT * FROM customer WHERE email='$email'";
    $result = mysqli_query($conn, $sql);
    
    if(mysqli_num_rows($result) === 1){
        // Xử lý mật khẩu
        $password_to_save = $hash_password ? password_hash($new_password, PASSWORD_BCRYPT) : $new_password;
        
        // Cập nhật mật khẩu
        $update_sql = "UPDATE customer SET pass='$password_to_save', verify_status=1 WHERE email='$email'";
        if(mysqli_query($conn, $update_sql)){
            echo "<div style='color: green; font-weight: bold;'>Đặt lại mật khẩu thành công!</div>";
            
            // Hiển thị thông tin tài khoản sau khi cập nhật
            $account_sql = "SELECT * FROM customer WHERE email='$email'";
            $account_result = mysqli_query($conn, $account_sql);
            if(mysqli_num_rows($account_result) === 1){
                $row = mysqli_fetch_assoc($account_result);
                echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>Thông tin tài khoản sau khi cập nhật:</h4>";
                echo "<p><strong>ID:</strong> " . $row['customer_id'] . "</p>";
                echo "<p><strong>Email:</strong> " . $row['email'] . "</p>";
                echo "<p><strong>Tên:</strong> " . $row['name'] . "</p>";
                echo "<p><strong>Mật khẩu:</strong> " . htmlspecialchars($row['pass']) . "</p>";
                echo "<p><strong>Độ dài mật khẩu:</strong> " . strlen($row['pass']) . " ký tự</p>";
                echo "<p><strong>Trạng thái xác thực:</strong> " . ($row['verify_status'] == 1 ? 'Đã xác thực' : 'Chưa xác thực') . "</p>";
                echo "</div>";
                
                echo "<div style='background-color: #e9f7ef; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>Thông tin đăng nhập:</h4>";
                echo "<p><strong>Email:</strong> " . $email . "</p>";
                echo "<p><strong>Mật khẩu:</strong> " . $new_password . "</p>";
                echo "<p><strong>Mật khẩu đã mã hóa:</strong> " . ($hash_password ? 'Có' : 'Không') . "</p>";
                echo "</div>";
            }
        }else{
            echo "<div style='color: red; font-weight: bold;'>Lỗi khi cập nhật mật khẩu: " . mysqli_error($conn) . "</div>";
        }
    }else{
        echo "<div style='color: red; font-weight: bold;'>Không tìm thấy tài khoản với email: " . $email . "</div>";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug tài khoản</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 20px;
        }
        .form-container {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        input[type="text"], input[type="password"], input[type="email"] {
            padding: 8px;
            width: 300px;
            margin-bottom: 10px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .links {
            margin-top: 20px;
        }
        .links a {
            display: inline-block;
            margin-right: 15px;
            color: #2196f3;
            text-decoration: none;
        }
        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <h2>Debug tài khoản</h2>
    
    <div class="warning">
        <h3>Lưu ý quan trọng:</h3>
        <p>Công cụ này dùng để kiểm tra và sửa lỗi tài khoản. Bạn có thể kiểm tra thông tin tài khoản và đặt lại mật khẩu.</p>
    </div>
    
    <div class="form-container">
        <h3>1. Kiểm tra thông tin tài khoản</h3>
        <form method="post" action="">
            <div>
                <label for="email">Email:</label><br>
                <input type="email" name="email" value="<EMAIL>" required>
            </div>
            <button type="submit" name="check_account">Kiểm tra tài khoản</button>
        </form>
    </div>
    
    <div class="form-container">
        <h3>2. Đặt lại mật khẩu</h3>
        <form method="post" action="">
            <div>
                <label for="email">Email:</label><br>
                <input type="email" name="email" value="<EMAIL>" required>
            </div>
            <div>
                <label for="new_password">Mật khẩu mới:</label><br>
                <input type="text" name="new_password" value="123456" required>
            </div>
            <div>
                <input type="checkbox" name="hash_password" id="hash_password">
                <label for="hash_password">Mã hóa mật khẩu</label>
            </div>
            <button type="submit" name="reset_password">Đặt lại mật khẩu</button>
        </form>
    </div>
    
    <div class="links">
        <a href="login.php">Quay lại trang đăng nhập</a>
        <a href="direct_login.php">Đăng nhập trực tiếp</a>
        <a href="../../customer/home.php">Trang chủ</a>
    </div>
</body>
</html>
