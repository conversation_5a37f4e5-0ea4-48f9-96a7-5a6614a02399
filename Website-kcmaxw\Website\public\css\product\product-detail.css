body {
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  color: #333;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.main-container {
  background-color: #f5f5f5;
  padding: 20px 0;
  flex: 1;
}

.product-body {
  width: 85%;
  background-color: white;
  margin: 0 auto 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.breadcrumb-container {
  width: 85%;
  margin: 0 auto 15px;
  padding: 10px 15px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.breadcrumb {
  margin: 0;
  padding: 0;
}

.breadcrumb-item a {
  color: #dc3545;
  text-decoration: none;
}

.breadcrumb-item a:hover {
  text-decoration: underline;
}

.breadcrumb-item.active {
  color: #6c757d;
}

.book-cover {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.book-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.book-image-container:hover {
  transform: scale(1.02);
}

.book-cover img {
  object-fit: contain;
  width: 100%;
  max-height: 500px;
  border-radius: 8px;
}

.product-detail {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
}

.book-title-quantity {
  display: flex;
  flex-direction: column;
  row-gap: 1.2em;
  padding: 20px 0;
}

.rating {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.stars {
  color: #ffc107;
  font-size: 1.2em;
}

.rating-count {
  color: #6c757d;
  font-size: 0.9em;
}

.book-price {
  font-size: 2.5em;
  color: #dc3545;
  font-weight: 700;
  margin: 10px 0;
}

.book-title {
  font-size: 2em;
  font-weight: 600;
  color: #343a40;
  line-height: 1.3;
}

.book-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin: 15px 0;
}

.book-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px dashed #e9ecef;
}

.info-label {
  color: #495057;
  font-weight: 500;
}

.info-label i {
  margin-right: 8px;
  color: #6c757d;
}

.info-value {
  color: #212529;
}

.return-policy {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  margin: 15px 0;
  font-size: 0.95em;
  border-left: 4px solid #0d6efd;
}

.return-policy i {
  color: #0d6efd;
  margin-right: 8px;
}

.quantity-section {
  margin: 20px 0;
}

.quantity-control {
  display: flex;
  align-items: center;
  max-width: 150px;
  margin-top: 8px;
}

.quantity-btn {
  width: 40px;
  height: 38px;
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  font-size: 1.2em;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.quantity-btn:hover {
  background-color: #e9ecef;
}

#set-quantity {
  text-align: center;
  border-radius: 0;
  height: 38px;
}

.buy-button {
  display: flex;
  gap: 10px;
  margin: 20px 0;
}

.buy-button button {
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.buy-button button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.buy-button button i {
  margin-right: 8px;
}

.btn-outline-danger {
  border-width: 2px;
}

.btn-danger {
  background-color: #dc3545;
}

.btn-danger:hover {
  background-color: #c82333;
}

.subtitle {
  font-weight: 600;
  font-size: 1.5em;
  margin-bottom: 20px;
  color: #343a40;
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
}

.subtitle i {
  margin-right: 10px;
  color: #dc3545;
}

.description {
  padding: 0;
}

.description-content {
  padding: 20px;
  line-height: 1.7;
  color: #495057;
}

.suggestion {
  padding: 0;
}

.suggestion-container {
  padding: 20px;
}

/* Tabs styling */
.product-tabs {
  width: 85%;
  margin: 0 auto 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.nav-tabs {
  border-bottom: 1px solid #e9ecef;
  padding: 0 20px;
  background-color: #f8f9fa;
}

.nav-tabs .nav-link {
  border: none;
  color: #495057;
  font-weight: 500;
  padding: 15px 20px;
  border-radius: 0;
  margin-right: 5px;
}

.nav-tabs .nav-link i {
  margin-right: 8px;
}

.nav-tabs .nav-link.active {
  color: #dc3545;
  border-bottom: 3px solid #dc3545;
  background-color: transparent;
}

.tab-content {
  padding: 0;
}

.tab-pane {
  padding: 0;
}

/* Rating and comments styling */
.rating-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.rating-average {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.rating-number {
  font-size: 2.5em;
  font-weight: 700;
  color: #dc3545;
}

.comment-button button {
  padding: 10px 20px;
}

.comment-sort {
  padding: 15px 20px;
  display: flex;
  gap: 10px;
  border-bottom: 1px solid #e9ecef;
}

.comment-sort button {
  font-size: 0.85em;
  padding: 6px 12px;
}

.comment-sort button.active {
  background-color: #dc3545;
  color: white;
  border-color: #dc3545;
}

.comments {
  padding: 20px;
}

.comment-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.comment-header {
  display: flex;
  margin-bottom: 10px;
}

.commentor-avatar {
  font-size: 2em;
  color: #6c757d;
  margin-right: 15px;
}

.commentor-info {
  flex: 1;
}

.comment-stars {
  color: #ffc107;
  font-size: 0.9em;
  margin: 5px 0;
}

.comment-date {
  font-size: 0.8em;
  color: #6c757d;
}

.comment-content {
  color: #495057;
  line-height: 1.6;
}

.no-comments {
  text-align: center;
  padding: 30px;
  color: #6c757d;
  font-style: italic;
}

/* Modal styling */
.modal-content {
  border-radius: 8px;
  border: none;
}

.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.modal-title {
  color: #343a40;
  font-weight: 600;
}

.rating-stars {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stars-input {
  font-size: 1.5em;
  color: #e0e0e0;
  cursor: pointer;
}

.stars-input i {
  margin: 0 3px;
  transition: color 0.2s;
}

.stars-input i:hover,
.stars-input i.active {
  color: #ffc107;
}

.rating-text {
  margin-top: 8px;
  font-size: 0.9em;
  color: #6c757d;
}

/* Product list styling */
.product-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.grid_row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

.grid_column-2-4 {
  padding: 0 10px;
  width: 20%;
  margin-bottom: 20px;
}

.home-product-item {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.home-product-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.home-product-item__img {
  padding-top: 100%;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  transition: transform 0.3s ease;
}

.home-product-item:hover .home-product-item__img {
  transform: scale(1.05);
}

.home-product-item__content {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.home-product-item__name {
  font-size: 1em;
  font-weight: 500;
  color: #343a40;
  margin-bottom: 10px;
  line-height: 1.4;
  height: 2.8em;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.home-product-item__price {
  margin-bottom: 10px;
}

.home-product-item__price-new {
  font-size: 1.1em;
  font-weight: 700;
  color: #dc3545;
}

.home-product-item__action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.home-product-item__like {
  color: #dc3545;
}

.home-product-item__rating {
  color: #ffc107;
  font-size: 0.8em;
}

.home-product-item__sold {
  font-size: 0.8em;
  color: #6c757d;
}

.home-product-item__btn {
  margin-top: auto;
}

.btn-view {
  width: 100%;
  padding: 8px 0;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-view:hover {
  background-color: #c82333;
}

/* Responsive styles */
@media (max-width: 992px) {
  .grid_column-2-4 {
    width: 25%;
  }
  
  .book-title {
    font-size: 1.8em;
  }
  
  .book-price {
    font-size: 2.2em;
  }
}

@media (max-width: 768px) {
  .grid_column-2-4 {
    width: 33.333%;
  }
  
  .main-container,
  .product-body,
  .product-tabs,
  .breadcrumb-container {
    width: 95%;
  }
  
  .row .col-5 {
    display: none;
  }
  
  .buy-button {
    justify-content: center;
  }
  
  .buy-button button {
    flex: 1;
  }
  
  .book-cover img {
    height: 300px;
  }
  
  .book-title {
    text-align: center;
    font-size: 1.5em;
  }
  
  .book-title-quantity .rating {
    justify-content: center;
  }
  
  .book-price {
    text-align: center;
    font-size: 2em;
  }
  
  .book-info-grid {
    grid-template-columns: 1fr;
  }
  
  .quantity-control {
    margin: 0 auto;
  }
  
  .rating-summary {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 576px) {
  .grid_column-2-4 {
    width: 50%;
  }
  
  .nav-tabs .nav-link {
    padding: 10px;
    font-size: 0.9em;
  }
  
  .subtitle {
    font-size: 1.2em;
  }
  
  .book-title {
    font-size: 1.3em;
  }
  
  .book-price {
    font-size: 1.8em;
  }
  
  .buy-button {
    flex-direction: column;
  }
  
  .comment-sort {
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 10px;
  }
}