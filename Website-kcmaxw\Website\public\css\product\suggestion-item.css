.suggested-cover {
  width: 150px;
  object-fit: contain;
  border-radius: 5px; 
}

.suggested-item {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  text-align: center; 
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: white;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.suggested-item:hover {
  transform: translateY(-5px); 
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); 
}

.sub-price {
  color: brown;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 5px;
}

.suggested-item_name {
  font-size: 17px;
  font-weight: 700;
  margin-top: 10px;
  color: #333; 
}

.flex-item:nth-child(2) {
  display: flex;
  flex-direction: column;
  row-gap: 10px;
  justify-content: center;
  align-items: center;
}

@media (max-width: 992px) {
  .suggested-item {
    flex-basis: 33.333333%; 
    padding: 15px;
  }

  .suggested-cover {
    width: 130px; 
  }
}

@media (max-width: 768px) {
  .sub-price {
    font-size: 2em;
  }

  .suggested-item_name {
    font-size: 1.4em;
  }

  .suggested-item {
    flex-basis: 50%; 
    padding: 15px;
  }

  .suggested-cover {
    width: 120px; 
  }
}

@media (max-width: 576px) {
  .suggested-item {
    flex-basis: 100%; 
    padding: 10px;
  }

  .suggested-cover {
    width: 100%;
  }

  .sub-price {
    font-size: 1.8em; 
  }

  .suggested-item_name {
    font-size: 1.2em; 
  }

  /* Fix col-3/col-9/row for mobile */
  .row {
    flex-direction: column !important;
    gap: 0 !important;
  }
  .col-3, .col-4, .col-9, .col-8, .col-xl-8, .col-xl-4, .col {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box;
  }
  .suggested-item, .flex-item, .suggested-cover {
    width: 100% !important;
    max-width: 100% !important;
    padding: 5px !important;
    margin: 0 !important;
    box-sizing: border-box;
  }
  .sub-price, .suggested-item_name {
    font-size: 1.1em !important;
  }
}
