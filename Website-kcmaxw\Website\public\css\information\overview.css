body {
  background-color: #eef2f3; 
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Arial', sans-serif; 
}
.main-container {
  background-color: #eef2f3;
  padding: 20px 0;
}
.overview {
  width: 80%;
  background-color: #ffffff;
  margin: 20px auto;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); 
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.overview:hover {
  transform: translateY(-5px); 
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}
.overview-title {
  font-weight: 700;
  font-size: 2em;
  color: #333; 
  margin-bottom: 10px;
  border-bottom: 2px solid #ffa500; 
  display: inline-block;
  padding-bottom: 5px;
}
.overview-subtitle {
  font-weight: 600;
  color: #ff6347; 
  margin-bottom: 15px;
  font-size: 1.2em;
}
.field-title {
  font-weight: 600;
  font-size: 1.5em;
  color: #555; 
  margin-top: 20px;
  margin-bottom: 10px;
  position: relative;
}
.field-title::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -5px;
  width: 50px;
  height: 3px;
  background-color: #ffa500; 
  border-radius: 2px;
}
.overview p {
  font-size: 1em;
  line-height: 1.6;
  color: #666; 
  margin-bottom: 15px;
}
@media (max-width: 768px) {
  .overview {
    width: 90%;
    padding: 20px;
  }
  .overview-title {
    font-size: 1.6em;
  }
  .overview-subtitle {
    font-size: 1em;
  }
  .field-title {
    font-size: 1.3em;
  }
}
/* Responsive for mobile devices */
@media (max-width: 576px) {
  .overview {
    width: 98% !important;
    padding: 10px !important;
  }
  .overview-title {
    font-size: 1.2em !important;
  }
  .overview-subtitle, .field-title {
    font-size: 1em !important;
  }
}
.address-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
  margin-top: 18px;
}

.address-card {
  display: flex;
  align-items: flex-start;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 4px 16px rgba(44, 62, 80, 0.10);
  padding: 18px 20px;
  gap: 16px;
  transition: box-shadow 0.2s;
}

.address-card:hover {
  box-shadow: 0 8px 32px rgba(44, 62, 80, 0.16);
}

.address-card-logo {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: #e3f4fd;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.address-card-logo img {
  width: 48px;
  height: 48px;
  object-fit: contain;
}

.address-card-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: 1.08em;
  color: #234;
  font-weight: 500;
  line-height: 1.5;
}
