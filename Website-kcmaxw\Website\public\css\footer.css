.footer {
  width: 97%;
  background: linear-gradient(135deg, #b3e0f7 60%, #e0f7fa 100%);
  padding: 2.5rem 2rem 6rem 2rem;
  margin-top: auto;
  border-radius: 1.5rem;
  box-shadow: 0 4px 32px rgba(44, 62, 80, 0.10);
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  overflow: hidden;
  margin-bottom: 30px;
  transition: box-shadow 0.3s;
}
.footer:hover {
  box-shadow: 0 8px 40px rgba(44, 62, 80, 0.18);
}
.footer-logo {
  background: rgba(255,255,255,0.7);
  border-radius: 1.2rem;
  padding: 1.5rem 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 12px rgba(44, 62, 80, 0.07);
  text-align: center;
}
.footer-logo img {
  width: 90px;
  height: 90px;
  object-fit: contain;
  border-radius: 1rem;
  background: #fff;
  padding: 10px;
  margin: 0 auto;
  display: block;
  box-shadow: 0 2px 8px rgba(44, 62, 80, 0.08);
  transition: transform 0.3s ease;
}
.footer-logo img:hover {
  transform: scale(1.05);
}
.footer-logo p {
  color: #234;
  font-size: 1.08rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}
.services {
  display: flex;
  flex-wrap: wrap;
  gap: 2.5rem;
  justify-content: center;
  margin-bottom: 1.5rem;
}
.services .col {
  min-width: 180px;
}
.services .col-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: #0a3556;
  margin-bottom: 1.1rem;
  letter-spacing: 1px;
}
.services .col ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.services .col ul a {
  color: #234;
  text-decoration: none;
  font-weight: 500;
  font-size: 1.05rem;
  display: block;
  padding: 6px 0;
  border-radius: 6px;
  transition: all 0.3s ease;
}
.services .col ul a:hover {
  background: #e0f7fa;
  color: #0077b6;
  padding-left: 5px;
}
.info {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2.5rem;
  margin-top: 2rem;
}
.info .col {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  font-size: 1.08rem;
  color: #234;
  font-weight: 500;
}
.info i {
  font-size: 1.3rem;
  color: #2196f3;
  background: #fff;
  border-radius: 8px;
  padding: 7px;
  box-shadow: 0 2px 8px rgba(44, 62, 80, 0.08);
  margin-right: 7px;
  transition: transform 0.3s ease;
}
.info i:hover {
  transform: scale(1.1);
}
.services .col-title, .info .col-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: #0a3556;
  margin-bottom: 1.1rem;
  letter-spacing: 1px;
}
.services .col ul li i {
  font-size: 1.3rem;
  color: #2196f3;
  margin-right: 8px;
  vertical-align: middle;
  transition: transform 0.3s ease;
}
.services .col ul li:hover i {
  transform: scale(1.1);
}
