<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

include('../config.php');

// Direct inclusion of PHPMailer files
require '../../../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../../../vendor/phpmailer/phpmailer/src/SMTP.php';
require '../../../vendor/phpmailer/phpmailer/src/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['newsletter_email'])) {
    $email = trim($_POST['newsletter_email']);
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Email không hợp lệ!']);
        exit;
    }

    // Gửi mail thông báo
    $subject = 'Đăng ký nhận thông báo mới từ ChillnFree BookShop';
    $body = '<h2>Chào Mừng Bạn Đến Với ChillnFree BookShop</h2><p>Bạn Sẽ Nhận Được Thông Báo Khi Có Những Cuốn Sách Độc Đáo Và Mới Nhất.</p>';

    try {
        // Check if email already exists
        $check_sql = "SELECT * FROM newsletter_subscribers WHERE email = ?";
        $check_stmt = $conn->prepare($check_sql);

        if (!$check_stmt) {
            throw new Exception("Database error: " . $conn->error);
        }

        $check_stmt->bind_param("s", $email);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            // Email already exists, update the timestamp
            $update_sql = "UPDATE newsletter_subscribers SET subscribe_date = NOW() WHERE email = ?";
            $update_stmt = $conn->prepare($update_sql);

            if (!$update_stmt) {
                throw new Exception("Database error: " . $conn->error);
            }

            $update_stmt->bind_param("s", $email);
            $update_stmt->execute();

            if ($update_stmt->affected_rows <= 0 && $update_stmt->errno != 0) {
                throw new Exception("Failed to update database: " . $update_stmt->error);
            }

            // Return success but with a different message
            echo json_encode(['success' => true, 'message' => 'Email của bạn đã được đăng ký trước đó!']);
            exit;
        } else {
            // Email doesn't exist, insert it
            $insert_sql = "INSERT INTO newsletter_subscribers (email, subscribe_date) VALUES (?, NOW())";
            $insert_stmt = $conn->prepare($insert_sql);

            if (!$insert_stmt) {
                throw new Exception("Database error: " . $conn->error);
            }

            $insert_stmt->bind_param("s", $email);
            $insert_stmt->execute();

            if ($insert_stmt->affected_rows <= 0 && $insert_stmt->errno != 0) {
                throw new Exception("Failed to save to database: " . $insert_stmt->error);
            }
        }

        // Now send the email for new subscribers only
        try {
            $mail = new PHPMailer(true);

            // Enable verbose debug output
            $mail->SMTPDebug = 0; // Set to 2 for detailed debugging

            // Server settings
            $mail->isSMTP();
            $mail->Host       = 'smtp.gmail.com';
            $mail->SMTPAuth   = true;
            $mail->Username   = '<EMAIL>';
            $mail->Password   = 'tflpnhxaxckznksk'; // This may need to be updated if it's an app password
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
            $mail->Port       = 465;

            // Recipients
            $mail->setFrom('<EMAIL>', 'ChillnFree BookShop');
            $mail->addAddress($email);

            // Content
            $mail->isHTML(true);
            $mail->CharSet = 'UTF-8'; // Ensure proper character encoding
            $mail->Subject = $subject;
            $mail->Body    = $body;

            $mail->send();

            // Return success message
            echo json_encode(['success' => true, 'message' => 'Đăng ký thành công! Vui lòng kiểm tra email của bạn.']);
        } catch (Exception $emailException) {
            // Log the email error but still return success since the database operation succeeded
            error_log("Email sending failed: " . $emailException->getMessage());
            echo json_encode(['success' => true, 'message' => 'Đăng ký thành công! Tuy nhiên, không thể gửi email xác nhận.']);
        }
    } catch (Exception $e) {
        // Log the error for debugging
        error_log("Newsletter Error: " . $e->getMessage());

        // Return a user-friendly error message
        echo json_encode(['success' => false, 'message' => 'Đã xảy ra lỗi. Vui lòng thử lại sau.']);
    }
    exit;
}
?>