<?php
    $change_add="";
    $count = 0;
    $addresses = array();

    // Lưu tất cả địa chỉ vào mảng
    while($row = mysqli_fetch_array($temp_add_run)){
        $addresses[] = $row;
    }

    // Tính số lượng địa chỉ
    $total = count($addresses);

    // Hiển thị địa chỉ theo cặp (2 cột)
    for($i = 0; $i < $total; $i++) {
        $row = $addresses[$i];

        // Mở hàng mới cho mỗi cặp địa chỉ
        if($i % 2 == 0) {
            echo "<tr class='address-row'>";
        }

        echo "
        <td class='address-cell'>
            <div class='address-card'>
                <div class='address-text'>$row[address]</div>
                <div class='address-actions'>
                    <a class='btn btn-primary delete-btn' href='../../controller/changeAddress_ctl.php?delete_id=$row[address_id]' role='button'>
                        <i class='fas fa-trash-alt'></i> Xóa
                    </a>
                </div>
            </div>
        </td>
        ";

        // Đóng hàng sau mỗi cặp hoặc khi đến địa chỉ cuối cùng
        if($i % 2 == 1 || $i == $total - 1) {
            // Nếu số lượng địa chỉ là lẻ và đây là địa chỉ cuối cùng, thêm một ô trống
            if($i == $total - 1 && $total % 2 == 1) {
                echo "<td class='address-cell'></td>";
            }
            echo "</tr>";
        }
    }

    // Nếu không có địa chỉ nào
    if($total == 0) {
        echo "<tr><td colspan='2' class='text-center'>Bạn chưa có địa chỉ nào. Vui lòng thêm địa chỉ mới.</td></tr>";
    }
?>