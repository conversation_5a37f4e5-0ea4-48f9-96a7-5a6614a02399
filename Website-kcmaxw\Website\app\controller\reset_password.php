<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

include('../../app/config.php');

// Kiểm tra kết nối cơ sở dữ liệu
echo "<h3>Kiểm tra kết nối cơ sở dữ liệu:</h3>";
if ($conn->connect_error) {
    die("Kết nối thất bại: " . $conn->connect_error);
} else {
    echo "Kết nối thành công đến cơ sở dữ liệu: " . $database . "<br>";
}

// Cập nhật mật khẩu
if (isset($_POST['reset_password'])) {
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    $new_password = mysqli_real_escape_string($conn, $_POST['new_password']);

    // Kiểm tra email
    $check_sql = "SELECT * FROM customer WHERE email='$email'";
    $check_result = mysqli_query($conn, $check_sql);

    if (mysqli_num_rows($check_result) === 1) {
        // Không mã hóa mật khẩu để giải quyết vấn đề đăng nhập
        // $hashed_password = password_hash($new_password, PASSWORD_BCRYPT);

        // Cập nhật mật khẩu (không mã hóa)
        $update_sql = "UPDATE customer SET pass='$new_password', verify_status=1 WHERE email='$email'";
        if (mysqli_query($conn, $update_sql)) {
            echo "<div style='color: green; font-weight: bold;'>Cập nhật mật khẩu thành công!</div>";
        } else {
            echo "<div style='color: red; font-weight: bold;'>Lỗi khi cập nhật mật khẩu: " . mysqli_error($conn) . "</div>";
        }
    } else {
        echo "<div style='color: red; font-weight: bold;'>Không tìm thấy email trong cơ sở dữ liệu.</div>";
    }
}
?>

<h3>Đặt lại mật khẩu:</h3>
<div style="background-color: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
    <p><strong>Lưu ý quan trọng:</strong> Công cụ này sẽ đặt lại mật khẩu về dạng <strong>không mã hóa</strong> để giải quyết vấn đề đăng nhập.</p>
</div>

<form method="post" action="" style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
    <div style="margin-bottom: 10px;">
        <label for="email">Email:</label><br>
        <input type="text" name="email" value="<EMAIL>" required style="padding: 8px; width: 300px;">
    </div>

    <div style="margin-bottom: 10px;">
        <label for="new_password">Mật khẩu mới:</label><br>
        <input type="text" name="new_password" value="123456" required style="padding: 8px; width: 300px;">
    </div>

    <input type="submit" name="reset_password" value="Đặt lại mật khẩu" style="padding: 10px 15px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
</form>

<div style="margin-top: 20px;">
    <a href="../views/LoginAndSignup/login.php" style="color: #2196f3; text-decoration: none; margin-right: 15px;">Quay lại trang đăng nhập</a>
    <a href="../customer/home.php" style="color: #2196f3; text-decoration: none;">Trang chủ</a>
</div>

<h3>Danh sách tài khoản:</h3>
<?php
// Hiển thị danh sách tài khoản
$result = $conn->query("SELECT customer_id, email, name, verify_status FROM customer");
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Email</th><th>Name</th><th>Verify Status</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['customer_id'] . "</td>";
        echo "<td>" . $row['email'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . $row['verify_status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Không thể lấy dữ liệu từ bảng customer: " . $conn->error . "<br>";
}
?>
