:root {
  --primary-color: #4361ee;
  --primary-light: #4895ef;
  --primary-dark: #3a56d4;
  --secondary-color: #f8f9fa;
  --accent-color: #3f37c9;
  --light-bg: #ffffff;
  --text-color: #333;
  --text-muted: #6c757d;
  --text-light: #f8f9fa;
  --box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
  --box-shadow-hover: 0 10px 20px rgba(0, 0, 0, 0.12);
  --border-radius: 12px;
  --input-border: #e0e0e0;
  --input-focus: #4361ee;
  --transition: all 0.3s ease;
  --success-color: #38b000;
  --error-color: #d90429;
  --card-bg: #ffffff;
  --card-border: #eaeaea;
}

body {
  background-color: var(--secondary-color);
  color: var(--text-color);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  margin: 0;
}

.content {
  width: 90%;
  max-width: 1200px;
  margin: 30px auto;
  padding: 30px;
  background-color: var(--light-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.main-container {
  background-color: var(--secondary-color);
  padding: 20px;
  min-height: 80vh;
}

/* Profile Container */
.profile-container {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  margin-bottom: 30px;
  border: 1px solid var(--card-border);
}

/* Profile Header */
.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: var(--text-light);
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
}

.profile-title {
  font-weight: 700;
  font-size: 22px;
  margin: 0;
  color: white;
  letter-spacing: 0.5px;
}

.btn-logout {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: var(--transition);
  display: flex;
  align-items: center;
}

.btn-logout:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.logout-link {
  text-decoration: none;
}

/* Profile Content */
.profile-content {
  display: flex;
  padding: 30px;
  gap: 30px;
}

/* Profile Header */
.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: var(--text-light);
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
}

.profile-title {
  font-weight: 700;
  font-size: 22px;
  margin: 0;
  color: white;
  letter-spacing: 0.5px;
}

/* Account Welcome Section */
.account-welcome {
  padding: 20px;
  background-color: var(--secondary-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: 20px;
}

.welcome-title {
  font-weight: 700;
  font-size: 22px;
  margin: 0 0 10px 0;
  color: var(--primary-color);
}

.welcome-subtitle {
  font-weight: 500;
  font-size: 16px;
  color: var(--text-color);
  margin: 0;
}

ul.selection-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

ul.selection-list li {
  padding: 12px;
  background-color: var(--secondary-color);
  border-radius: 6px;
  box-shadow: var(--box-shadow);
  cursor: pointer;
  transition: var(--transition);
}

ul.selection-list li:hover,
ul.selection-list li.active {
  background-color: var(--primary-color);
  color: #fff;
  font-weight: bold;
}

.account-info, .address-book, .my-orders, .my-reviews {
  background-color: var(--light-bg);
  border-radius: var(--border-radius);
  padding: 25px;
  box-shadow: var(--box-shadow);
  margin-bottom: 25px;
}

.account-info h2 {
  font-size: 26px;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 20px;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 8px;
}

.address-book ul,
.my-reviews ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.address-book ul li,
.my-reviews ul li {
  padding: 12px;
  background-color: var(--secondary-color);
  border-radius: 6px;
  box-shadow: var(--box-shadow);
  margin-bottom: 12px;
}

.my-orders table {
  width: 100%;
  border-collapse: collapse;
  font-size: 15px;
}

.my-orders table th,
.my-orders table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.my-orders table th {
  background-color: var(--primary-color);
  color: #ffffff;
}
/* Form field icons and styling */
/* Profile Details */
.profile-details {
  flex: 1;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: var(--box-shadow);
  border: 1px solid var(--card-border);
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.form-row {
  display: flex;
  gap: 25px;
}

.form-group {
  flex: 1;
  position: relative;
}

.form-label {
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-color);
  font-size: 15px;
}

.input-with-icon {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 16px;
}

.form-input {
  width: 100%;
  padding: 14px 14px 14px 42px;
  border: 1px solid var(--input-border);
  border-radius: 8px;
  font-size: 15px;
  transition: var(--transition);
  background-color: var(--secondary-color);
  color: var(--text-color);
  font-weight: 500;
}

.form-input:focus {
  border-color: var(--input-focus);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
  outline: none;
  background-color: #fff;
}

.toggle-password {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  cursor: pointer;
  transition: var(--transition);
  font-size: 16px;
}

.toggle-password:hover {
  color: var(--primary-color);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.btn-save {
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: var(--transition);
  display: flex;
  align-items: center;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 6px rgba(67, 97, 238, 0.2);
}

.btn-save:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(67, 97, 238, 0.3);
  background: linear-gradient(to right, var(--primary-dark), var(--primary-color));
}

.btn-save:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(67, 97, 238, 0.2);
}

/* Form validation */
.form-input.is-valid {
  border-color: var(--success-color);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2338b000' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  padding-right: 30px;
}

.form-input.is-invalid {
  border-color: var(--error-color);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23d90429'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23d90429' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  padding-right: 30px;
}

/* Responsive for mobile devices */
@media (max-width: 992px) {
  .profile-content {
    flex-direction: column;
    gap: 20px;
  }

  /* Removed profile-avatar-section */
}

@media (max-width: 768px) {
  .content {
    width: 95%;
    padding: 20px;
    margin: 15px auto;
  }

  .main-container {
    padding: 15px;
  }

  .profile-header {
    padding: 15px 20px;
  }

  .profile-title {
    font-size: 20px;
  }

  .profile-content {
    padding: 20px;
  }

  .account-welcome {
    padding: 15px;
  }

  .welcome-title {
    font-size: 20px;
  }

  .welcome-subtitle {
    font-size: 15px;
  }

  .profile-details {
    padding: 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .btn-save {
    width: 100%;
    justify-content: center;
  }

  .form-actions {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .content {
    width: 100%;
    padding: 15px;
    margin: 10px auto;
    border-radius: 8px;
  }

  .main-container {
    padding: 10px;
  }

  .row {
    flex-direction: column;
    gap: 15px;
  }

  .col-3, .col-4, .col-9, .col-8 {
    width: 100%;
    max-width: 100%;
    flex: 0 0 100%;
    padding: 0;
  }

  .profile-container {
    margin-bottom: 15px;
  }

  .profile-header {
    padding: 12px 15px;
  }

  .profile-title {
    font-size: 18px;
  }

  .btn-logout {
    padding: 6px 12px;
    font-size: 13px;
  }

  .profile-content {
    padding: 15px;
  }

  /* Removed avatar related styles */

  .profile-details {
    padding: 15px;
  }

  .account-welcome {
    padding: 12px;
  }

  .welcome-title {
    font-size: 18px;
  }

  .welcome-subtitle {
    font-size: 14px;
  }

  .form-label {
    font-size: 14px;
  }

  .form-input {
    padding: 12px 12px 12px 36px;
    font-size: 14px;
  }

  .input-icon {
    font-size: 14px;
  }

  .btn-save {
    padding: 12px 20px;
    font-size: 14px;
  }
}

/* Fix header and card layout for mobile */
@media (max-width: 576px) {
  /* Header: logo + site name + search + icons */
  .header-wrapper {
    flex-direction: column !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 8px 4px !important;
  }
  .logo-container {
    flex-direction: column !important;
    align-items: center !important;
    gap: 4px !important;
    width: 100% !important;
  }
  .logo img {
    width: 70px !important;
    height: 70px !important;
    margin: 0 auto !important;
    display: block !important;
  }
  .site-name {
    font-size: 18px !important;
    text-align: center !important;
    margin: 0 !important;
    width: 100% !important;
    font-weight: 700 !important;
    letter-spacing: 1px !important;
  }
  .menu-search {
    width: 100% !important;
    margin: 0 auto 8px auto !important;
    display: flex !important;
    justify-content: center !important;
  }
  .menu-search input[type="text"] {
    width: 100% !important;
    min-width: 0 !important;
    font-size: 15px !important;
    padding: 8px 36px 8px 12px !important;
    border-radius: 20px !important;
  }
  .icons {
    width: 100% !important;
    justify-content: center !important;
    gap: 18px !important;
    margin-bottom: 8px !important;
  }
  .header-icon {
    width: 28px !important;
    height: 28px !important;
  }

  /* Card/product list: shrink padding, font, image */
  .home-product-item, .featured-product-card {
    padding: 8px 4px !important;
    margin: 8px 0 !important;
    border-radius: 10px !important;
    min-width: 0 !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }
  .home-product-item__img, .featured-product-img {
    width: 90px !important;
    height: 90px !important;
    min-width: 90px !important;
    min-height: 90px !important;
    margin: 0 auto 8px auto !important;
    display: block !important;
    border-radius: 8px !important;
  }
  .home-product-item__name, .featured-product-name {
    font-size: 13px !important;
    min-height: unset !important;
    text-align: center !important;
    margin: 4px 0 2px 0 !important;
  }
  .home-product-item__price, .featured-product-price {
    font-size: 14px !important;
    text-align: center !important;
    margin-bottom: 4px !important;
  }
  .swiper, .featured-products-swiper {
    padding: 0 !important;
  }
  .swiper-slide {
    padding: 0 !important;
    margin: 0 !important;
  }
  /* Footer shrink */
  .footer {
    padding: 8px 4px 16px 4px !important;
    border-radius: 10px !important;
  }
}
