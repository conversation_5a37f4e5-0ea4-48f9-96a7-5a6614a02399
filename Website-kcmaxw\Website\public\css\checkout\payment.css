:root {
  --primary: #4A90E2;
  --primary-dark: #3a7bc8;
  --secondary: #FF6B6B;
  --accent: #6C63FF;
  --success: #28a745;
  --warning: #ffc107;
  --danger: #dc3545;
  --bg-light: #f8fafc;
  --white: #ffffff;
  --text: #333;
  --text-light: #666;
  --border: #e1e8ed;
  --shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  --shadow-hover: 0 15px 35px rgba(0, 0, 0, 0.12);
  --radius: 12px;
  --radius-sm: 8px;
  --transition: all 0.3s ease-in-out;
}

body {
  background: linear-gradient(135deg, var(--bg-light) 0%, #e0e7ff 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text);
}

.main-container {
  background: transparent;
  padding: 30px 0;
}

.cart {
  background-color: var(--white);
  width: 92%;
  max-width: 1200px;
  margin: auto;
  padding: 35px;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: 40px;
  position: relative;
  overflow: hidden;
}

.cart::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary) 0%, var(--accent) 100%);
}

.subtitle {
  font-size: 22px;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
}

.subtitle::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--primary);
  border-radius: 10px;
}

.section-wrapper {
  background-color: #fdfefe;
  border-radius: var(--radius);
  padding: 25px;
  box-shadow: var(--shadow);
  margin-bottom: 25px;
}

/* Progress Steps */
.progress-container {
  margin: 20px auto 40px;
  max-width: 600px;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  position: relative;
  margin-bottom: 30px;
}

.progress-steps::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 3px;
  background: #e1e8ed;
  transform: translateY(-50%);
  z-index: 0;
}

.progress-step {
  position: relative;
  z-index: 1;
  background: #fff;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #4A90E2;
  border: 2px solid #4A90E2;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.progress-step.active {
  background: #4A90E2;
  color: #fff;
}

.progress-step-label {
  position: absolute;
  top: 45px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  white-space: nowrap;
  color: #4A90E2;
  font-weight: 500;
}

/* Payment Methods */
.payment-methods {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.payment-method {
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  padding: 15px;
  width: calc(50% - 10px);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.payment-method:hover {
  border-color: #4A90E2;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.payment-method.active {
  border-color: #4A90E2;
  background-color: rgba(74, 144, 226, 0.05);
}

.payment-method-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.payment-method-header img {
  width: 40px;
  height: 40px;
  margin-right: 10px;
  object-fit: contain;
}

.payment-method-title {
  font-weight: 600;
  font-size: 16px;
}

.payment-method-description {
  font-size: 14px;
  color: #666;
}

.payment-method-radio {
  position: absolute;
  top: 15px;
  right: 15px;
}

/* QR Code */
.qr-code-container {
  display: none;
  margin-top: 20px;
  text-align: center;
}

.qr-code-container.active {
  display: block;
  animation: fadeIn 0.5s ease;
}

.qr-code-container img {
  max-width: 250px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.qr-instructions {
  margin-top: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 10px;
  text-align: left;
}

.qr-instructions ol {
  padding-left: 20px;
}

.qr-instructions li {
  margin-bottom: 8px;
}

/* Coupon Section */
.coupon-section {
  margin-bottom: 20px;
}

.coupon-input {
  display: flex;
  gap: 10px;
}

.coupon-input input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
}

.coupon-input button {
  padding: 10px 20px;
  background-color: #4A90E2;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.coupon-input button:hover {
  background-color: #357ABD;
}

.applied-coupons {
  margin-top: 10px;
}

.coupon-tag {
  display: inline-flex;
  align-items: center;
  background-color: #e6f0fb;
  color: #4A90E2;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 14px;
  margin-right: 10px;
  margin-bottom: 10px;
}

.coupon-tag i {
  margin-left: 5px;
  cursor: pointer;
}

/* Order Summary */
.order-summary {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
}

.order-summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.order-summary-total {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  font-size: 18px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed #e1e8ed;
}

/* Payment Actions */
.payment-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.payment-actions button {
  padding: 12px 25px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-back {
  background-color: #fff;
  color: #4A90E2;
  border: 1px solid #4A90E2;
}

.btn-back:hover {
  background-color: #f0f7ff;
}

.btn-confirm {
  background-color: #4A90E2;
  color: #fff;
  border: none;
}

.btn-confirm:hover {
  background-color: #357ABD;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .payment-method {
    width: 100%;
  }
  
  .payment-actions {
    flex-direction: column;
    gap: 15px;
  }
  
  .payment-actions button {
    width: 100%;
  }
  
  .cart {
    padding: 20px;
    width: 95%;
  }
  
  .section-wrapper {
    padding: 15px;
  }
  
  .subtitle {
    font-size: 20px;
  }
  
  .subtitle::after {
    width: 40px;
  }
}
