<?php
include('../../controller/cart_ctl.php');
?>
<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Giỏ hàng - ChillnFree BookShop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
    <link rel="stylesheet" href="../../../public/css/header.css">
    <link rel="stylesheet" href="../../../public/css/footer.css">
    <link rel="stylesheet" href="../../../public/css/mobile.css">
    <script src="https://kit.fontawesome.com/9d371022aa.js" crossorigin="anonymous"></script>
    <style>
        :root {
            --primary: #4A90E2;
            --primary-light: #e6f0fb;
            --primary-dark: #357ABD;
            --secondary: #e53935;
            --text-dark: #2d3748;
            --text-light: #718096;
            --bg-light: #f8f9fa;
            --border-color: #e2e8f0;
            --radius: 8px;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: var(--text-dark);
        }

        .main-container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 15px;
        }

        .checkout-container {
            background-color: white;
            border-radius: var(--radius);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 30px;
        }

        /* Progress Steps */
        .progress-steps {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            position: relative;
        }

        .progress-steps::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: #e2e8f0;
            z-index: 1;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
            width: 33.333%;
        }

        .progress-step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: white;
            border: 2px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 8px;
            color: #718096;
        }

        .progress-step.active .progress-step-number {
            background-color: var(--primary);
            border-color: var(--primary);
            color: white;
        }

        .progress-step-label {
            font-size: 14px;
            font-weight: 500;
            color: #718096;
        }

        .progress-step.active .progress-step-label {
            color: var(--primary);
            font-weight: 600;
        }

        /* Cart Items */
        .cart-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary);
            display: flex;
            align-items: center;
        }

        .cart-title .badge {
            margin-left: 10px;
            background-color: var(--primary);
            font-size: 12px;
            padding: 5px 10px;
            border-radius: 20px;
        }

        .cart-items {
            margin-bottom: 20px;
        }

        .cart-item {
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 15px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .cart-item-image {
            width: 80px;
            height: 100px;
            object-fit: cover;
            border-radius: 4px;
            margin-right: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .cart-item-details {
            flex: 1;
        }

        .cart-item-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--text-dark);
            font-size: 16px;
        }

        .cart-item-meta {
            font-size: 13px;
            color: var(--text-light);
            margin-bottom: 5px;
            line-height: 1.4;
        }

        .cart-item-price {
            font-weight: 600;
            color: var(--secondary);
            font-size: 16px;
            margin-left: auto;
            margin-right: 15px;
        }

        .cart-item-quantity {
            display: flex;
            align-items: center;
            margin-left: 10px;
        }

        .cart-item-quantity input {
            width: 40px;
            text-align: center;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 5px;
            font-size: 14px;
        }

        .cart-item-remove {
            color: var(--secondary);
            cursor: pointer;
            margin-left: 15px;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .cart-item-remove:hover {
            background-color: rgba(229, 57, 53, 0.1);
        }

        /* Order Summary */
        .order-summary {
            background-color: white;
            border-radius: var(--radius);
            padding: 20px;
            border: 1px solid var(--border-color);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .order-summary-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--primary);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }

        .order-summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
        }

        .order-summary-label {
            color: var(--text-dark);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .order-summary-label i {
            color: var(--primary);
            font-size: 14px;
            width: 18px;
        }

        .order-summary-value {
            font-weight: 600;
            color: var(--text-dark);
        }

        .delivery-estimate {
            color: var(--primary);
            font-weight: 500;
        }

        .order-summary-total {
            border-top: 1px solid var(--border-color);
            margin-top: 15px;
            padding-top: 15px;
            font-weight: 700;
            font-size: 16px;
        }

        .order-summary-total .order-summary-value {
            color: var(--secondary);
            font-size: 18px;
        }

        /* Additional Services */
        .additional-services {
            margin-top: 20px;
        }

        .additional-services h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary);
        }

        .service-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 10px 15px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            background-color: white;
        }

        .service-item-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .service-item-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .service-item label {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
            cursor: pointer;
        }

        .service-item input[type="checkbox"] {
            margin-right: 5px;
            transform: scale(1.2);
            accent-color: var(--primary);
        }

        .service-item input[type="number"] {
            width: 50px;
            text-align: center;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 5px;
        }

        .service-description {
            font-size: 12px;
            color: var(--text-light);
        }

        .service-price {
            font-weight: 600;
            color: var(--text-dark);
        }

        /* Buttons */
        .btn-primary {
            background-color: var(--primary);
            border: none;
            padding: 12px 20px;
            border-radius: var(--radius);
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(74, 144, 226, 0.2);
        }

        .btn-primary:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-outline {
            background-color: transparent;
            border: 1px solid var(--primary);
            padding: 12px 20px;
            border-radius: var(--radius);
            color: var(--primary);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-outline:hover {
            background-color: var(--primary-light);
            transform: translateY(-2px);
        }

        .checkout-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            gap: 15px;
        }

        @media (max-width: 768px) {
            .checkout-actions {
                flex-direction: column;
            }

            .btn-outline {
                order: 2;
            }

            .btn-primary {
                order: 1;
            }
        }

        /* Empty Cart */
        .empty-cart {
            text-align: center;
            padding: 30px;
            background-color: white;
            border-radius: var(--radius);
            margin-bottom: 20px;
        }

        .empty-cart i {
            font-size: 50px;
            color: var(--text-light);
            margin-bottom: 15px;
        }

        .empty-cart h3 {
            font-size: 20px;
            margin-bottom: 10px;
            color: var(--text-dark);
        }

        .empty-cart p {
            color: var(--text-light);
            margin-bottom: 20px;
        }
    </style>
</head>

<body>
    <?php include '../components/header.php'; ?>

    <div class="main-container">
        <form action="../../controller/checkout_ctl.php" method="post">
            <div class="checkout-container">
                <!-- Progress Steps -->
                <div class="progress-steps">
                    <div class="progress-step active">
                        <div class="progress-step-number">1</div>
                        <div class="progress-step-label">Giỏ hàng</div>
                    </div>
                    <div class="progress-step">
                        <div class="progress-step-number">2</div>
                        <div class="progress-step-label">Thanh toán</div>
                    </div>
                    <div class="progress-step">
                        <div class="progress-step-number">3</div>
                        <div class="progress-step-label">Hoàn tất</div>
                    </div>
                </div>

                <style>
                    /* Thêm khoảng cách giữa các nhãn trong thanh tiến trình */
                    .progress-step-label {
                        margin-top: 8px;
                        width: 100%;
                        text-align: center;
                    }
                </style>

                <div class="row">
                    <div class="col-md-8">
                        <!-- Cart Items -->
                        <div class="cart-title">
                            Giỏ hàng của bạn
                            <span class="badge"><?= $number_item ?> sản phẩm</span>
                        </div>

                        <?php if ($number_item > 0) : ?>
                            <div class="cart-items">
                                <?php
                                $total_price = 0; // Khởi tạo lại biến tổng tiền

                                while ($row = mysqli_fetch_array($db_cart_run)) {
                                    $book_id = $row['book_id'];
                                    $quantity = $row['quantity'];
                                    $db_book = "SELECT * FROM book WHERE book_id='$book_id' LIMIT 1";
                                    $db_book_run = mysqli_query($conn, $db_book);
                                    $book_info = mysqli_fetch_array($db_book_run);

                                    // Xác định thể loại
                                    if ($book_info['category_id'] == 1) {
                                        $category = "Tiểu thuyết";
                                    } else if ($book_info['category_id'] == 2) {
                                        $category = "Sách kĩ năng";
                                    } else if ($book_info['category_id'] == 3) {
                                        $category = "Thiếu nhi";
                                    } else if ($book_info['category_id'] == 4) {
                                        $category = "Sách nước ngoài";
                                    } else if ($book_info['category_id'] == 5) {
                                        $category = "Sách giáo khoa";
                                    } else if ($book_info['category_id'] == 6) {
                                        $category = "Sách tham khảo";
                                    } else if ($book_info['category_id'] == 7) {
                                        $category = "Sách lịch sử";
                                    } else if ($book_info['category_id'] == 8) {
                                        $category = "Sách khoa học";
                                    } else if ($book_info['category_id'] == 9) {
                                        $category = "Sách nghệ thuật";
                                    } else if ($book_info['category_id'] == 10) {
                                        $category = "Sách kinh tế";
                                    } else {
                                        $category = "Sách nước ngoài";
                                    }

                                    $item_total_price_ip = $quantity * $book_info['price'];
                                    $item_total_price = number_format($item_total_price_ip, 0, ',', '.') . "đ";

                                    // Cộng dồn vào tổng tiền
                                    $total_price += $item_total_price_ip;

                                    echo "
                                    <div class='cart-item'>
                                        <img src='$book_info[cover_image]' alt='$book_info[name]' class='cart-item-image'>
                                        <div class='cart-item-details'>
                                            <div class='cart-item-title'>$book_info[name]</div>
                                            <div class='cart-item-meta'>
                                                <div>Tác giả: $book_info[author]</div>
                                                <div>Thể loại: $category</div>
                                            </div>
                                        </div>
                                        <div class='cart-item-price'>$item_total_price</div>
                                        <div class='cart-item-quantity'>
                                            <input type='hidden' class='book_id' value='$book_id'>
                                            <input type='hidden' class='tt_price' value='$item_total_price'>
                                            <input type='number' class='itemQty' value='$quantity' min='1'>
                                        </div>
                                        <a href='../../controller/checkout_ctl.php?delete_book=$book_id' class='cart-item-remove'>
                                            <i class='fas fa-trash-alt'></i>
                                        </a>
                                    </div>
                                    ";
                                }
                                ?>
                            </div>
                        <?php else : ?>
                            <div class="empty-cart">
                                <i class="fas fa-shopping-cart"></i>
                                <h3>Giỏ hàng của bạn đang trống</h3>
                                <p>Hãy thêm sản phẩm vào giỏ hàng để tiếp tục mua sắm</p>
                                <a href="../customer/home.php" class="btn btn-primary">
                                    <i class="fas fa-shopping-bag"></i> Tiếp tục mua sắm
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="col-md-4">
                        <!-- Order Summary -->
                        <div class="order-summary">
                            <div class="order-summary-title">Tổng kết đơn hàng</div>

                            <div class="order-summary-row">
                                <div class="order-summary-label">
                                    <i class="fas fa-shopping-basket"></i> Tạm tính:
                                </div>
                                <div class="order-summary-value" id="dp-it-price">
                                    <?= number_format($total_price, 0, ',', '.') ?>đ
                                </div>
                                <input hidden="hidden" type="number" id="it-price" value="<?= $total_price ?>">
                            </div>

                            <div class="order-summary-row">
                                <div class="order-summary-label">
                                    <i class="fas fa-truck"></i> Phí vận chuyển:
                                </div>
                                <div class="order-summary-value" id="dp-sv-price">
                                    0đ
                                </div>
                            </div>

                            <div class="order-summary-total">
                                <div class="order-summary-label">
                                    <i class="fas fa-money-bill-wave"></i> Tổng thanh toán:
                                </div>
                                <div class="order-summary-value" id="dp-total-price">
                                    <?= number_format($total_price, 0, ',', '.') ?>đ
                                </div>
                                <input hidden="hidden" type="number" name="all-total-price" id="total-price-input" value="<?= $total_price ?>">
                            </div>
                        </div>

                        <!-- Additional Services -->
                        <div class="additional-services">
                            <h3>Dịch vụ đi kèm</h3>
                            <div class="service-item">
                                <div class="service-item-left">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input service" type="checkbox" role="switch" id="flexSwitchCheckChecked">
                                    </div>
                                    <div>
                                        <div>Bọc sách cao cấp</div>
                                        <div class="service-description">Bảo vệ sách khỏi bụi và nước</div>
                                    </div>
                                </div>
                                <div class="service-item-right">
                                    <div>
                                        <input type="number" id="book-wrap-quantity" value="1" min="1" max="<?= $number_item ?>">
                                    </div>
                                    <div class="service-price" id="book-wrap-fee">0đ</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="checkout-actions">
                    <a href="../customer/home.php" class="btn btn-outline">
                        <i class="fas fa-arrow-left"></i> Tiếp tục mua sắm
                    </a>
                    <?php if ($number_item > 0) : ?>
                        <a href="payment.php" class="btn btn-primary">
                            Tiến hành thanh toán <i class="fas fa-arrow-right"></i>
                        </a>
                    <?php else : ?>
                        <button type="button" class="btn btn-primary" disabled>
                            Tiến hành thanh toán <i class="fas fa-arrow-right"></i>
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </form>
    </div>

    <?php include '../components/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../js/jquery.js"></script>
    <script src="../../js/function.js"></script>
    <script src="../../js/provinces.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Cập nhật lại tính phí khi chọn tỉnh thành
            const serviceFeeElement = document.getElementById('dp-sv-price');
            const totalPriceElement = document.getElementById('dp-total-price');
            const itemPriceElement = document.getElementById('it-price');
            const bookWrapCheckbox = document.getElementById('flexSwitchCheckChecked');
            const bookWrapQuantity = document.getElementById('book-wrap-quantity');
            const bookWrapFeeElement = document.getElementById('book-wrap-fee');

            function calculateAndDisplayFees() {
                // Tính phí bọc sách
                const isWrapChecked = bookWrapCheckbox && bookWrapCheckbox.checked;
                const wrapQty = isWrapChecked ? Number(bookWrapQuantity.value) : 0;
                const bookWrapFee = wrapQty * 20000;
                if (bookWrapFeeElement) bookWrapFeeElement.textContent = bookWrapFee.toLocaleString('vi-VN') + 'đ';

                // Tổng phí dịch vụ = ship + bọc sách
                const serviceFee = bookWrapFee;
                serviceFeeElement.textContent = serviceFee.toLocaleString('vi-VN') + 'đ';

                // Tổng số tiền
                const itemPrice = parseInt(itemPriceElement.value, 10);
                const totalPrice = itemPrice + serviceFee;
                totalPriceElement.textContent = totalPrice.toLocaleString('vi-VN') + 'đ';

                // Cập nhật giá trị ẩn
                document.getElementById('total-price-input').value = totalPrice;
            }

            if (bookWrapCheckbox && bookWrapQuantity) {
                bookWrapCheckbox.addEventListener('change', calculateAndDisplayFees);
                bookWrapQuantity.addEventListener('input', calculateAndDisplayFees);
                bookWrapQuantity.addEventListener('change', calculateAndDisplayFees);
            }

            calculateAndDisplayFees();

            // Cập nhật số lượng sản phẩm
            $('.itemQty').on('change', function() {
                var $el = $(this).closest('.cart-item');
                var book_id = $el.find('.book_id').val();
                var qty = $el.find('.itemQty').val();

                // Gửi yêu cầu AJAX để cập nhật số lượng
                $.ajax({
                    url: '../../controller/checkout_ctl.php',
                    method: 'POST',
                    cache: false,
                    data: {
                        qty: qty,
                        book_id: book_id,
                        update_cart: true
                    },
                    success: function(response) {
                        // Tải lại trang để cập nhật giá
                        location.reload();
                    }
                });
            });
        });
    </script>
</body>
</html>
