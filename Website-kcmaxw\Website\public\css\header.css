.header {
  background-color: #ADD8E6;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 10px 0;
  position: sticky;
  top: 0;
  z-index: 999;
}
.header-wrapper {
  width: 90%;
  max-width: 1200px;
  margin: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.logo-container {
  display: flex;
  align-items: center;
}
.logo img {
  height: 50px;
  object-fit: contain;
  border-radius: 1.2rem;
  background-color: white;
  padding: 5px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #ddd;
  transition: transform 0.3s ease;
}
.site-name a {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  text-decoration: none;
  margin-left: 12px;
  font-family: 'Poppins', 'Helvetica Neue', sans-serif;
  letter-spacing: 0.5px;
}
.logo img:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}
.menu-search {
  display: flex;
  align-items: center;
  position: relative;
}
.menu-search input[type="text"] {
  padding: 0.6em 2.5em 0.6em 1em;
  border-radius: 20px;
  border: 1px solid #ccc;
  outline: none;
  font-size: 1em;
  width: 420px;
  transition: all 0.3s ease;
}
.menu-search input[type="text"]:focus {
  border-color: #888;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
}
.search-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
}
.search-button img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
  transition: transform 0.3s ease;
}
.search-button:hover img {
  transform: scale(1.1);
}
.icons {
  display: flex;
  gap: 1em;
  align-items: center;
}
.header-icon {
  width: 2em;
  height: 2em;
  transition: transform 0.3s ease;
}
.icon-button {
  padding: 0.5em;
  border-radius: 50%;
  background-color: transparent;
  transition: background-color 0.3s, box-shadow 0.3s;
}
.icon-button:hover {
  background-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}
.icon-button:hover .header-icon {
  transform: scale(1.1);
}
.guest-button {
  display: flex;
  gap: 1em;
  align-items: center;
  margin-left: 15px;
}
.guest-button a button {
  min-width: 100px;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 8px;
  padding: 8px 20px;
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(44, 62, 80, 0.08);
}
.guest-button a .btn-primary {
  background: linear-gradient(90deg, #2196f3 60%, #21cbf3 100%);
  color: #fff;
}
.guest-button a .btn-primary:hover {
  background: linear-gradient(90deg, #21cbf3 0%, #2196f3 100%);
  color: #fff;
  box-shadow: 0 4px 16px rgba(33, 203, 243, 0.15);
  transform: translateY(-2px);
}
.guest-button a .btn-danger {
  background: linear-gradient(90deg, #e53935 60%, #e35d5b 100%);
  color: #fff;
}
.guest-button a .btn-danger:hover {
  background: linear-gradient(90deg, #e35d5b 0%, #e53935 100%);
  color: #fff;
  box-shadow: 0 4px 16px rgba(229, 57, 53, 0.15);
  transform: translateY(-2px);
}
.menu-wrapper {
  position: relative;
}
.menu {
  opacity: 0;
  position: absolute;
  z-index: 1000;
  background-color: #f0f8ff;
  border-radius: 10px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  padding: 10px;
  width: max-content;
  margin-top: 5px;
  transition: opacity 0.3s ease;
}
.menu h5 {
  color: #333;
  margin-bottom: 10px;
  font-weight: 600;
  text-align: center;
}
.menu ul {
  list-style: none;
  padding-left: 0;
}
.category-item a {
  text-decoration: none;
  color: #333;
  display: block;
  padding: 8px 10px;
  border-radius: 6px;
  transition: all 0.3s ease;
}
.category-item a:hover {
  background-color: #d0e9f5;
  color: #0077b6;
  padding-left: 15px;
}
.animate {
  transition: all 0.2s linear;
}
@media (max-width: 992px) {
  html {
    font-size: 70%;
  }
  .logo img {
    height: 5vh;
  }
  .menu-search input[type="text"] {
    width: 150px;
  }
}
@media (max-width: 576px) {
  html {
    font-size: 60%;
  }
  .header-wrapper {
    flex-direction: column !important;
    gap: 10px !important;
    align-items: flex-start !important;
    padding: 10px 5px !important;
  }
  .logo img {
    max-width: 80% !important;
    height: auto !important;
  }
  .site-name {
    font-size: 16px !important;
  }
  .menu-search {
    width: 100% !important;
  }
  .menu-search input[type="text"] {
    width: 100% !important;
  }
.logo-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.site-name {
    font-size: 20px;
    font-weight: 700;
}
.site-name a {
    text-decoration: none;
    color: #333;
    transition: color 0.3s ease;
}
.site-name a:hover {
    color: #4285f4;
}
.header-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
@media (max-width: 768px) {
    .site-name {
        font-size: 18px;
    }
}
@media (max-width: 576px) {
    .site-name {
        font-size: 16px;
    }
    .logo img {
        max-width: 80%;
    }
}
}