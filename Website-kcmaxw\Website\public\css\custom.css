/* custom.css - Nâng cấp giao diện với hiệu ứng hiện đại */
body {
  background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
  font-family: 'Segoe UI', '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
}

.home_container {
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18);
  border-radius: 18px;
  background: rgba(255,255,255,0.95);
  padding: 32px 16px;
  margin-top: 32px;
}

.featured-title, .newsletter-title {
  font-weight: 700;
  letter-spacing: 1px;
  color: #3b2f63;
  text-shadow: 0 2px 8px #e0e7ff;
}

.featured-product-card {
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(59,47,99,0.10);
  background: #fff;
  padding: 18px 12px;
  transition: transform 0.2s, box-shadow 0.2s;
  text-align: center;
}
.featured-product-card:hover {
  transform: translateY(-8px) scale(1.04);
  box-shadow: 0 8px 32px 0 rgba(59,47,99,0.18);
}
.featured-product-img {
  max-width: 100px;
  border-radius: 8px;
  margin-bottom: 10px;
}
.featured-product-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d1e6b;
  margin-bottom: 4px;
}
.featured-product-price {
  color: #d7263d;
  font-weight: 700;
  font-size: 1.05rem;
}

.newsletter-section {
  background: linear-gradient(90deg, #fbb13c 0%, #d7263d 100%);
  border-radius: 14px;
  padding: 32px 18px;
  margin: 40px 0 0 0;
  box-shadow: 0 2px 16px 0 rgba(251,177,60,0.10);
  text-align: center;
}
.newsletter-title {
  color: #fff;
  margin-bottom: 18px;
}
.newsletter-form {
  display: flex;
  justify-content: center;
  gap: 12px;
}
.newsletter-input {
  border-radius: 8px;
  border: none;
  padding: 10px 16px;
  min-width: 220px;
  font-size: 1rem;
  box-shadow: 0 2px 8px 0 rgba(59,47,99,0.06);
}
.newsletter-button {
  background: #3b2f63;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 24px;
  font-weight: 600;
  letter-spacing: 1px;
  transition: background 0.2s;
}
.newsletter-button:hover {
  background: #d7263d;
}

/* Nâng cấp phân trang */
.pagination .page-link {
  border-radius: 8px !important;
  margin: 0 2px;
  color: #3b2f63;
  font-weight: 500;
  transition: background 0.2s, color 0.2s;
}
.pagination .page-item.active .page-link {
  background: #fbb13c;
  color: #fff;
  border: none;
}
.pagination .page-link:hover {
  background: #d7263d;
  color: #fff;
}

/* Nâng cấp nav danh mục */
.home_category {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(59,47,99,0.06);
  padding: 18px 10px;
}
.category_heading {
  color: #d7263d;
  font-weight: 700;
  font-size: 1.1rem;
  margin-bottom: 10px;
}
.category-item__link {
  color: #3b2f63;
  font-weight: 500;
  border-radius: 6px;
  padding: 6px 10px;
  display: block;
  transition: background 0.2s, color 0.2s;
}
.category-item__link:hover {
  background: #fbb13c;
  color: #fff;
}

#back-home {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  font-size: 16px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

#back-home:hover {
  background-color: #5a6268;
}
