<?php
// Bắt đầu file PHP xử lý đăng ký tài khoản người dùng

// <PERSON><PERSON> gồm file cấu hình cơ sở dữ liệu và khởi tạo session
include('../../config.php');

// Bao gồm file model chứa hàm kiểm tra tính hợp lệ của dữ liệu đăng ký
include('../../model/signup_model.php');

// Bao gồm file model chứa hàm gửi email xác thực
include('../../model/sendMail_model.php');

// Kiểm tra xem người dùng đã đăng nhập chưa
if(isset($_SESSION['customer_id'])){
    // Nếu đã đăng nhập, chuyển hướng về trang khách hàng
    header('location: ../customer');
    exit(0); // Dừng thực thi script
}

// Khởi tạo các biến để lưu trữ dữ liệu form và thông báo
$name =""; // Biến lưu tên người dùng
$email =""; // Biến lưu email người dùng
$succMess=""; // Biến lưu thông báo thành công

// Kiểm tra xem form đăng ký có được gửi không
if(isset($_POST['signup']))
{
    // Lấy và làm sạch dữ liệu tên từ form để tránh SQL injection
    $name = mysqli_real_escape_string($conn, $_POST['name']);

    // Lấy và làm sạch dữ liệu email từ form để tránh SQL injection
    $email = mysqli_real_escape_string($conn, $_POST['mail']);

    // Lấy và làm sạch dữ liệu mật khẩu từ form để tránh SQL injection
    $pass = mysqli_real_escape_string($conn, $_POST['pass']);

    // Lấy và làm sạch dữ liệu xác nhận mật khẩu từ form để tránh SQL injection
    $cpass = mysqli_real_escape_string($conn, $_POST['cpass']);

    // Tạo mã xác thực ngẫu nhiên bằng cách hash một số ngẫu nhiên
    $code = mysqli_real_escape_string($conn, md5(rand()));

    // Mã hóa mật khẩu bằng thuật toán BCRYPT trước khi lưu vào cơ sở dữ liệu
    $hashedPass = password_hash($pass, PASSWORD_BCRYPT);

    // Gọi hàm kiểm tra tính hợp lệ của dữ liệu đăng ký
    $mess = checkValid($name, $email, $pass, $cpass, $conn);

    // Kiểm tra xem có lỗi nào không (nếu $mess rỗng = không có lỗi)
    if(empty($mess)){
        // Tạo câu truy vấn SQL để thêm khách hàng mới vào cơ sở dữ liệu
        // verify_status = 1 nghĩa là tài khoản đã được xác thực ngay lập tức
        $sql = "INSERT INTO customer(name,email,pass,code,verify_status) VALUES ('$name','$email','$hashedPass','$code',1)";

        // Thực thi câu truy vấn
        $result = mysqli_query($conn,$sql);

        // Kiểm tra xem việc thêm dữ liệu có thành công không
        if($result){
            // Gửi email xác thực đến địa chỉ email đã đăng ký
            sendmail_Verify( $email, $code);

            // Tạo thông báo thành công với link đăng nhập
            $succMess = "Đăng ký thành công. Tài khoản đã được xác thực. <a href='login.php' class='alert-link'>Nhấn vào đây để đăng nhập</a>";

            // Xóa dữ liệu trong các biến để làm sạch form
            $name = "";
            $email = "";
        }
    }
}
// Kết thúc file PHP
?>