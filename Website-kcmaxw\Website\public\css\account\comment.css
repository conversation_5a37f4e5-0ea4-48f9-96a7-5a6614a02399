:root {
  --primary: #4A90E2;
  --bg-light: #f1f6f5;
  --white: #ffffff;
  --text: #333;
  --shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  --radius: 10px;
  --transition: all 0.3s ease-in-out;
}

body {
  background-color: var(--bg-light);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.content {
  width: 90%;
  max-width: 1200px;
  margin: 30px auto;
}

.main-container {
  background-color: var(--bg-light);
}

.comment {
  background-color: var(--white);
  border-radius: var(--radius);
  margin: 15px 0;
  padding: 1.5em;
  box-shadow: var(--shadow);
}

.comment-title {
  font-weight: 700;
  font-size: 20px;
  color: var(--primary);
  margin-bottom: 1em;
  border-bottom: 2px solid var(--primary);
  padding-bottom: 0.7em;
  letter-spacing: 0.5px;
}

.box-wrapper {
  box-shadow: var(--shadow);
  padding: 15px;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  gap: 1em;
  background-color: #fafcff;
  transition: var(--transition);
}

.box-wrapper:hover {
  transform: translateY(-2px);
  background-color: #f0f7ff;
}

.box-wrapper img {
  height: 80px;
  width: 80px;
  object-fit: contain;
  border-radius: 6px;
  background-color: #fff;
  border: 1px solid #eee;
}

.box-wrapper div:nth-child(2),
.box-wrapper div:nth-child(3) {
  width: 33.33%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  font-size: 15px;
  line-height: 1.5;
}

.box-wrapper button {
  margin-left: auto;
  background-color: var(--primary);
  color: #fff;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: var(--transition);
}

.box-wrapper button:hover {
  background-color: #3b78c7;
}

.book-comment {
  display: none;
}

/* Responsive for mobile devices */
@media (max-width: 576px) {
  .comment {
    padding: 10px !important;
  }
  .box-wrapper {
    flex-direction: column !important;
    gap: 0.5em !important;
    align-items: flex-start !important;
  }
  .box-wrapper img {
    width: 60px !important;
    height: 60px !important;
  }
  .box-wrapper div:nth-child(2),
  .box-wrapper div:nth-child(3) {
    width: 100% !important;
  }
}
