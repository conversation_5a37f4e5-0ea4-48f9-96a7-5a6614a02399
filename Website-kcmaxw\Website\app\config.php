<?php
// Bắt đầu file PHP để cấu hình kết nối cơ sở dữ liệu

// <PERSON><PERSON> báo thông tin máy chủ cơ sở dữ liệu
$servername = "localhost"; // Địa chỉ máy chủ MySQL (localhost = máy chủ cục bộ)
$username = "root"; // Tên người dùng MySQL (root = tài khoản quản trị mặc định)
$password = ""; // Mật khẩu MySQL (trống cho XAMPP mặc định)
$database = "dulieusach"; // Tên cơ sở dữ liệu sẽ kết nối

// Tạo kết nối đến cơ sở dữ liệu MySQL sử dụng MySQLi
$conn = new mysqli($servername, $username, $password, $database);

// Kiểm tra xem kết nối có thành công hay không
if ($conn->connect_error) {
    // Nếu kết nối thất bại, dừng chương trình và hiển thị thông báo lỗi
    die("Connection failed: " . $conn->connect_error);
}

// Khởi tạo phiên làm việc (session) để lưu trữ thông tin người dùng
session_start();
