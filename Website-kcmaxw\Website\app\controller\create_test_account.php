<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

include('../../app/config.php');

// Tạo tài khoản test
$name = "Test User";
$email = "<EMAIL>";
$password = "123456";
$code = md5(rand());

// Mã hóa mật khẩu
$hashed_password = password_hash($password, PASSWORD_BCRYPT);

// Kiểm tra email đã tồn tại chưa
$check_sql = "SELECT * FROM customer WHERE email='$email'";
$check_result = mysqli_query($conn, $check_sql);

if (mysqli_num_rows($check_result) > 0) {
    echo "Email đã tồn tại trong hệ thống!<br>";
    
    // Cập nhật mật khẩu
    $update_sql = "UPDATE customer SET pass='$hashed_password', verify_status=1 WHERE email='$email'";
    if (mysqli_query($conn, $update_sql)) {
        echo "Đã cập nhật mật khẩu cho tài khoản test!<br>";
    } else {
        echo "Lỗi khi cập nhật mật khẩu: " . mysqli_error($conn) . "<br>";
    }
} else {
    // Thêm tài khoản mới
    $insert_sql = "INSERT INTO customer (name, email, pass, code, verify_status, phone) VALUES ('$name', '$email', '$hashed_password', '$code', 1, '')";
    if (mysqli_query($conn, $insert_sql)) {
        echo "Đã tạo tài khoản test thành công!<br>";
    } else {
        echo "Lỗi khi tạo tài khoản test: " . mysqli_error($conn) . "<br>";
    }
}

// Hiển thị thông tin tài khoản
$account_sql = "SELECT * FROM customer WHERE email='$email'";
$account_result = mysqli_query($conn, $account_sql);
if (mysqli_num_rows($account_result) === 1) {
    $row = mysqli_fetch_assoc($account_result);
    echo "<h3>Thông tin tài khoản test:</h3>";
    echo "ID: " . $row['customer_id'] . "<br>";
    echo "Email: " . $row['email'] . "<br>";
    echo "Tên: " . $row['name'] . "<br>";
    echo "Mật khẩu (đã mã hóa): " . $row['pass'] . "<br>";
    echo "Độ dài mật khẩu: " . strlen($row['pass']) . " ký tự<br>";
    echo "Trạng thái xác thực: " . ($row['verify_status'] == 1 ? 'Đã xác thực' : 'Chưa xác thực') . "<br>";
    
    // Kiểm tra mật khẩu
    if (password_verify($password, $row['pass'])) {
        echo "Kiểm tra mật khẩu: Mật khẩu khớp khi sử dụng password_verify!<br>";
    } else {
        echo "Kiểm tra mật khẩu: Mật khẩu không khớp khi sử dụng password_verify!<br>";
    }
}

echo "<h3>Thông tin đăng nhập:</h3>";
echo "Email: <EMAIL><br>";
echo "Mật khẩu: 123456<br>";
echo "<a href='../views/LoginAndSignup/login.php'>Đi đến trang đăng nhập</a>";
?>
