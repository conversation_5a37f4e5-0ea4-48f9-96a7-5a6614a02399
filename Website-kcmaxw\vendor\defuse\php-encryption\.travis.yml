language: php
sudo: false

matrix:
  fast_finish: true
  include:
    - php: "5.6"
      env: 
        - USE_PSALM=0
        - BUILD_PHAR=0
    - php: "7.0"
      env: 
        - USE_PSALM=0
        - BUILD_PHAR=0
    - php: "7.1"
      env: 
        - USE_PSALM=0
        - BUILD_PHAR=0
    - php: "7.2"
      env: 
        - USE_PSALM=0
        - BUILD_PHAR=0
    - php: "7.3"
      env:
        - USE_PSALM=0
        - BUILD_PHAR=0
    - php: "7.4"
      env: 
        - USE_PSALM=1
        - BUILD_PHAR=0
    - php: "8.0"
      # psalm currently doesn't like our \[#SensitiveParameter]s
      env: 
        - USE_PSALM=0
        - BUILD_PHAR=0
    - php: "8.1"
      # psalm currently doesn't like our \[#SensitiveParameter]s
      env: 
        - USE_PSALM=0
        - BUILD_PHAR=1
    - php: "8.2"
      env:
        - USE_PSALM=1
        - BUILD_PHAR=1
      dist: focal
    - php: "nightly"
      env: 
        - USE_PSALM=1
        - BUILD_PHAR=1
    - php: "hhvm"
      env: 
        - USE_PSALM=1
        - BUILD_PHAR=1
  allow_failures:
    - php: "nightly"
    - php: "hhvm"
    # Travis-CI's 8.2 is currently broken, see:
    # https://github.com/defuse/php-encryption/pull/506#issuecomment-1594084107
    #- php: "8.2"
install:
    - composer install
before_script:
    - echo "xdebug.mode = coverage" > extra_php_config.ini
    - phpenv config-add extra_php_config.ini
script:
    - ./test.sh
    - if [[ $BUILD_PHAR -eq 1 ]]; then mkdir /tmp/box; fi
    - if [[ $BUILD_PHAR -eq 1 ]]; then chmod 755 /tmp/box; fi
    - if [[ $BUILD_PHAR -eq 1 ]]; then curl -LSs https://github.com/box-project/box/releases/download/4.3.8/box.phar -o /tmp/box/box; fi
    - if [[ $BUILD_PHAR -eq 1 ]]; then chmod 755 /tmp/box/box; fi
    - if [[ $BUILD_PHAR -eq 1 ]]; then PATH="$PATH:/tmp/box/" which box; fi
    - if [[ $BUILD_PHAR -eq 1 ]]; then PATH="$PATH:/tmp/box/" make -C dist/ build-phar; fi
    - if [[ $BUILD_PHAR -eq 1 ]]; then ./test.sh dist/phar-testing-autoload.php; fi
    - if [[ $USE_PSALM -eq 1 ]]; then composer require --with-all-dependencies --dev "vimeo/psalm:dev-master"; fi
    - if [[ $USE_PSALM -eq 1 ]]; then composer install; fi
    - if [[ $USE_PSALM -eq 1 ]]; then vendor/bin/psalm; fi

after_success:
    - cat coverage1.xml
    - cat coverage2.xml
    - bash <(curl -s https://codecov.io/bash) -Z
