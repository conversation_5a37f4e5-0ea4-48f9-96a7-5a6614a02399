<?php
        include('../../config.php');
        include('../../model/ctmAccount_model.php');
        
        $item_per_page = 10;
        $total_page;
        $current_page = (!empty($_GET['page'])) ? $_GET['page'] : 1;
        $offset = ($current_page - 1) * $item_per_page;
        
        // Xác định cách sắp xếp
        $sort_clause = "ORDER BY `book_id` DESC";
        if(isset($_GET['sort'])) {
            $sort_type = $_GET['sort'];
            switch($sort_type) {
                case 'Phổ biến':
                    // Sắp xếp theo phổ biến (giả sử có trường view_count)
                    $sort_clause = "ORDER BY (SELECT COUNT(*) FROM `contain` WHERE `contain`.`book_id` = `book`.`book_id`) DESC";
                    break;
                case 'Mới nhất':
                    $sort_clause = "ORDER BY `book_id` DESC";
                    break;
                case '<PERSON>án chạy':
                    $sort_clause = "ORDER BY (SELECT SUM(quantity) FROM `contain` WHERE `contain`.`book_id` = `book`.`book_id`) DESC";
                    break;
            }
        }
        
        // Sắp xếp theo giá
        if(isset($_GET['price_order'])) {
            $price_order = $_GET['price_order'];
            if($price_order == 'asc') {
                $sort_clause = "ORDER BY `price` ASC";
            } else if($price_order == 'desc') {
                $sort_clause = "ORDER BY `price` DESC";
            }
        }
        
        if(isset($_GET['price'])){
            $price_range = $_GET['price'];
            $price_sql = '';
            if($price_range == '0-150000') {
                $price_sql = 'price >= 0 AND price <= 150000';
            } else if($price_range == '150000-250000') {
                $price_sql = 'price > 150000 AND price <= 250000';
            } else if($price_range == '250000-350000') {
                $price_sql = 'price > 250000 AND price <= 350000';
            } else if($price_range == '350000-500000') {
                $price_sql = 'price > 350000 AND price <= 500000';
            } else if($price_range == '500000-up') {
                $price_sql = 'price > 500000';
            }
            $db_book = "SELECT * FROM `book` WHERE $price_sql $sort_clause LIMIT $item_per_page OFFSET $offset";
            $db_book_run = mysqli_query($conn, $db_book);
            $total_records = mysqli_query($conn, "SELECT * FROM `book` WHERE $price_sql");
            $total_records = mysqli_num_rows($total_records);
            $total_page = ceil($total_records / $item_per_page);
            if(!$db_book_run){
                header('location: ../LoginAndSignup/home.php');
                exit(0);
            } 
        } else if(isset($_GET['category_id'])){
            $db_book="SELECT * FROM `book` WHERE `category_id`=$_GET[category_id] $sort_clause"
                    . " LIMIT " . $item_per_page . " OFFSET " . $offset;
            $db_book_run = mysqli_query($conn,$db_book);

            $total_records = mysqli_query($conn,"SELECT * FROM `book` WHERE `category_id`=$_GET[category_id]");
            $total_records = mysqli_num_rows($total_records);
            $total_page = ceil($total_records / $item_per_page);

            if(!$db_book_run){
                header('location: ../LoginAndSignup/home.php');
                exit(0);
            } 
        }else if(isset($_GET['search']) && !empty($_GET['search'])){
            $db_book="SELECT * FROM `book` WHERE `name` LIKE '%$_GET[search]%' OR `author` LIKE '%$_GET[search]%' $sort_clause"
                    . " LIMIT " . $item_per_page . " OFFSET " . $offset;
            $db_book_run = mysqli_query($conn,$db_book);
            
            $total_records = mysqli_query($conn,"SELECT * FROM `book` WHERE `name` LIKE '%$_GET[search]%' OR `author` LIKE '%$_GET[search]%'");
            $total_records = mysqli_num_rows($total_records);
            $total_page = ceil($total_records / $item_per_page);

            if(!$db_book_run){
                header('location: ../LoginAndSignup/home.php');
                exit(0);
            } 
        }else{
            $db_book="SELECT * FROM `book` $sort_clause"
                        . " LIMIT " . $item_per_page . " OFFSET " . $offset;
            $db_book_run = mysqli_query($conn,$db_book);

            $total_records = mysqli_query($conn,"SELECT * FROM `book`");
            $total_records = mysqli_num_rows($total_records);
            $total_page = ceil($total_records / $item_per_page);

            if(!$db_book_run){
                header('location: ../LoginAndSignup/home.php');
                exit(0);
            }   
        }

        if(isset($_GET['book_id'])){
            $book_id = $_GET['book_id'];
            $book="SELECT * FROM `book` WHERE book_id=$book_id LIMIT 1";
            $book_run = mysqli_query($conn,$book);
            if($db_book_run){
                $book_info = mysqli_fetch_array($book_run);
            }else{
                header('location: ../LoginAndSignup/home.php');
                exit(0);
            }
            // GET COMMENT
            $cmt="SELECT * FROM `rate` WHERE book_id=$book_id";
            $cmt_run = mysqli_query($conn,$cmt);
            $number_of_rate = mysqli_num_rows($cmt_run);
            if(!$cmt_run){
                header('location: ../LoginAndSignup/home.php');
                exit(0);
            }

            $daban = "SELECT * FROM `contain` WHERE book_id=$book_id";
            $daban_run = mysqli_query($conn,$daban);
            $soluong_daban = mysqli_num_rows($daban_run);
            if(!$daban_run){
                header('location: ../LoginAndSignup/home.php');
                exit(0);
            }
        }
?>
