<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

include('../../app/config.php');

// <PERSON>ể<PERSON> tra kết nối cơ sở dữ liệu
echo "<h3>Kiểm tra kết nối cơ sở dữ liệu:</h3>";
if ($conn->connect_error) {
    die("Kết nối thất bại: " . $conn->connect_error);
} else {
    echo "Kết nối thành công đến cơ sở dữ liệu: " . $database . "<br>";
}

// Kiểm tra cấu trúc bảng customer
echo "<h3>Cấu trúc bảng customer:</h3>";
$result = $conn->query("DESCRIBE customer");
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Không thể lấy cấu trúc bảng customer: " . $conn->error . "<br>";
}

// Kiểm tra dữ liệu trong bảng customer
echo "<h3>Dữ liệu trong bảng customer:</h3>";
$result = $conn->query("SELECT customer_id, email, name, LENGTH(pass) as pass_length, verify_status FROM customer");
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Email</th><th>Name</th><th>Password Length</th><th>Verify Status</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['customer_id'] . "</td>";
        echo "<td>" . $row['email'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . $row['pass_length'] . "</td>";
        echo "<td>" . $row['verify_status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Không thể lấy dữ liệu từ bảng customer: " . $conn->error . "<br>";
}

// Tạo tài khoản mới
if (isset($_POST['create_account'])) {
    $name = mysqli_real_escape_string($conn, $_POST['name']);
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    $password = mysqli_real_escape_string($conn, $_POST['password']);
    $code = md5(rand());
    
    // Kiểm tra email đã tồn tại chưa
    $check_sql = "SELECT * FROM customer WHERE email='$email'";
    $check_result = mysqli_query($conn, $check_sql);
    
    if (mysqli_num_rows($check_result) > 0) {
        echo "<div style='color: red; font-weight: bold;'>Email đã tồn tại trong hệ thống!</div>";
    } else {
        // Mã hóa mật khẩu
        $hashed_password = password_hash($password, PASSWORD_BCRYPT);
        
        // Thêm tài khoản mới
        $insert_sql = "INSERT INTO customer (name, email, pass, code, verify_status) VALUES ('$name', '$email', '$hashed_password', '$code', 1)";
        if (mysqli_query($conn, $insert_sql)) {
            echo "<div style='color: green; font-weight: bold;'>Thêm tài khoản mới thành công!</div>";
            
            // Hiển thị thông tin tài khoản vừa tạo
            $new_account_sql = "SELECT * FROM customer WHERE email='$email'";
            $new_account_result = mysqli_query($conn, $new_account_sql);
            if (mysqli_num_rows($new_account_result) === 1) {
                $row = mysqli_fetch_assoc($new_account_result);
                echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>Thông tin tài khoản mới:</h4>";
                echo "<p><strong>ID:</strong> " . $row['customer_id'] . "</p>";
                echo "<p><strong>Email:</strong> " . $row['email'] . "</p>";
                echo "<p><strong>Tên:</strong> " . $row['name'] . "</p>";
                echo "<p><strong>Mật khẩu (đã mã hóa):</strong> " . htmlspecialchars($row['pass']) . "</p>";
                echo "<p><strong>Độ dài mật khẩu:</strong> " . strlen($row['pass']) . " ký tự</p>";
                echo "<p><strong>Trạng thái xác thực:</strong> " . ($row['verify_status'] == 1 ? 'Đã xác thực' : 'Chưa xác thực') . "</p>";
                echo "</div>";
            }
        } else {
            echo "<div style='color: red; font-weight: bold;'>Lỗi khi thêm tài khoản mới: " . mysqli_error($conn) . "</div>";
        }
    }
}

// Cập nhật cấu trúc bảng
if (isset($_POST['update_table'])) {
    $sql = "ALTER TABLE `customer` MODIFY `pass` varchar(255) NOT NULL";
    if ($conn->query($sql) === TRUE) {
        echo "<div style='color: green; font-weight: bold;'>Cập nhật cấu trúc bảng thành công!</div>";
    } else {
        echo "<div style='color: red; font-weight: bold;'>Lỗi khi cập nhật cấu trúc bảng: " . $conn->error . "</div>";
    }
}

// Kiểm tra đăng nhập với tài khoản vừa tạo
if (isset($_POST['test_login'])) {
    $email = mysqli_real_escape_string($conn, $_POST['test_email']);
    $pass = mysqli_real_escape_string($conn, $_POST['test_password']);
    
    echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>Kiểm tra đăng nhập:</h4>";
    echo "<p><strong>Email nhập vào:</strong> " . $email . "</p>";
    echo "<p><strong>Mật khẩu nhập vào:</strong> " . $pass . "</p>";
    
    // Kiểm tra email
    $sql = "SELECT * FROM customer WHERE email='$email'";
    $result = mysqli_query($conn, $sql);
    
    if (mysqli_num_rows($result) === 1) {
        $row = mysqli_fetch_assoc($result);
        echo "<p style='color: green;'><strong>Tìm thấy email trong cơ sở dữ liệu.</strong></p>";
        echo "<p><strong>Mật khẩu trong cơ sở dữ liệu:</strong> " . htmlspecialchars($row['pass']) . "</p>";
        echo "<p><strong>Độ dài mật khẩu đã mã hóa:</strong> " . strlen($row['pass']) . " ký tự</p>";
        
        // Kiểm tra mật khẩu
        if (password_verify($pass, $row['pass'])) {
            echo "<p style='color: green;'><strong>Mật khẩu khớp khi sử dụng password_verify!</strong></p>";
            
            // Kiểm tra trạng thái xác thực
            if ($row['verify_status'] == 1) {
                echo "<p style='color: green;'><strong>Tài khoản đã được xác thực.</strong></p>";
                echo "<p style='color: green; font-weight: bold;'>Đăng nhập thành công!</p>";
            } else {
                echo "<p style='color: red;'><strong>Tài khoản chưa được xác thực.</strong></p>";
            }
        } else {
            echo "<p style='color: red;'><strong>Mật khẩu không khớp khi sử dụng password_verify!</strong></p>";
            
            // Thử kiểm tra trực tiếp
            if ($pass === $row['pass']) {
                echo "<p style='color: green;'><strong>Mật khẩu khớp khi so sánh trực tiếp!</strong></p>";
            } else {
                echo "<p style='color: red;'><strong>Mật khẩu không khớp khi so sánh trực tiếp!</strong></p>";
            }
        }
    } else {
        echo "<p style='color: red;'><strong>Không tìm thấy email trong cơ sở dữ liệu.</strong></p>";
    }
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Đăng Ký</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 20px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .form-container {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        input[type="text"], input[type="password"], input[type="email"] {
            padding: 8px;
            width: 300px;
            margin-bottom: 10px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h2>Công cụ Debug Đăng Ký</h2>
    
    <div class="form-container">
        <h3>1. Tạo tài khoản mới</h3>
        <form method="post" action="">
            <div>
                <label for="name">Tên:</label><br>
                <input type="text" name="name" required>
            </div>
            <div>
                <label for="email">Email:</label><br>
                <input type="email" name="email" required>
            </div>
            <div>
                <label for="password">Mật khẩu:</label><br>
                <input type="password" name="password" required>
            </div>
            <button type="submit" name="create_account">Tạo tài khoản</button>
        </form>
    </div>
    
    <div class="form-container">
        <h3>2. Cập nhật cấu trúc bảng</h3>
        <p>Cập nhật độ dài trường pass trong bảng customer từ varchar(50) thành varchar(255).</p>
        <form method="post" action="">
            <button type="submit" name="update_table">Cập nhật cấu trúc bảng</button>
        </form>
    </div>
    
    <div class="form-container">
        <h3>3. Kiểm tra đăng nhập với tài khoản vừa tạo</h3>
        <form method="post" action="">
            <div>
                <label for="test_email">Email:</label><br>
                <input type="email" name="test_email" required>
            </div>
            <div>
                <label for="test_password">Mật khẩu:</label><br>
                <input type="password" name="test_password" required>
            </div>
            <button type="submit" name="test_login">Kiểm tra đăng nhập</button>
        </form>
    </div>
</body>
</html>
