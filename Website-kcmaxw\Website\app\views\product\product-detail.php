<?php
include('../../controller/product_ctl.php');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bookstore</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../../public/css/header.css">
    <link rel="stylesheet" href="../../../public/css/footer.css">
    <link rel="stylesheet" href="../../../public/css/product/product-detail.css">
    <link rel="stylesheet" href="../../../public/css/product/suggestion-item.css">
    <link rel="stylesheet" href="../../../public/css/mobile.css">
    <script src="https://kit.fontawesome.com/9d371022aa.js" crossorigin="anonymous"></script>
</head>

<body>
    <?php include '../components/header.php'; ?>
    <div class="main-container">
        <nav aria-label="breadcrumb" class="breadcrumb-container">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../customer/home.php">Trang chủ</a></li>
                <li class="breadcrumb-item"><a href="#">Sách</a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo $book_info['name']; ?></li>
            </ol>
        </nav>

        <div class="product-body">
            <div class="product-detail">
                <div class="container">
                    <div class="row">
                        <div class="col-md-5 book-cover">
                            <div class="book-image-container">
                                <img src="<?php echo $book_info['cover_image']; ?>" alt="<?php echo $book_info['name']; ?>">
                            </div>
                        </div>
                        <div class="col book-title-quantity">
                            <div class="book-title"><?php echo $book_info['name']; ?></div>
                            <div class="rating">
                                <div class="stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <div class="rating-count"><?php echo $number_of_rate . " đánh giá"; ?></div>
                            </div>

                            <div class="book-price"><?php echo number_format($book_info['price'], 0, ',', '.') . "đ"; ?></div>

                            <div class="book-info-grid">
                                <div class="book-info-item">
                                    <span class="info-label"><i class="fas fa-building"></i> Nhà xuất bản:</span>
                                    <span class="info-value"><?php echo $book_info['publisher']; ?></span>
                                </div>
                                <div class="book-info-item">
                                    <span class="info-label"><i class="fas fa-user-edit"></i> Tác giả:</span>
                                    <span class="info-value"><?php echo $book_info['author']; ?></span>
                                </div>
                                <div class="book-info-item">
                                    <span class="info-label"><i class="fas fa-box-open"></i> Tình trạng:</span>
                                    <span class="info-value"><span class="badge bg-success">Còn hàng</span></span>
                                </div>
                                <div class="book-info-item">
                                    <span class="info-label"><i class="fas fa-shopping-cart"></i> Đã bán:</span>
                                    <span class="info-value">
                                        <span class="badge bg-secondary"><?php echo $soluong_daban; ?></span>
                                    </span>
                                </div>
                            </div>

                            <div class="return-policy">
                                <i class="fas fa-exchange-alt"></i> <strong>Chính sách đổi trả: </strong> Đổi trả sản phẩm trong 30 ngày <span class="badge rounded-pill text-bg-primary">Xem thêm</span>
                            </div>

                            <div class="quantity-section">
                                <strong><i class="fas fa-cubes"></i> Số lượng</strong>
                                <div class="quantity-control">
                                    <button class="quantity-btn" id="decrease-quantity">-</button>
                                    <input type="number" class="form-control" id="set-quantity" name="set-quantity" value="1" min="1">
                                    <button class="quantity-btn" id="increase-quantity">+</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-5"></div>
                        <div class="col">
                            <div class="buy-button">
                                <?php
                                if (isset($_SESSION['customer_id'])) {
                                ?>
                                    <button type="button" class="btn btn-outline-danger" id="addcart-bt" onclick="addCart(<?php echo $book_info['book_id'] ?>)">
                                        <i class="fa-solid fa-cart-plus"></i>Thêm vào giỏ hàng
                                    </button>
                                    <button type="button" class="btn btn-danger" id="buynow" onclick="buynow(<?php echo $book_info['book_id'] ?>)">
                                        <i class="fas fa-bolt"></i> Mua ngay
                                    </button>
                                    <button type="button" class="btn btn-secondary" id="back-home" onclick="window.location.href='../customer/home.php'">
                                        <i class="fas fa-arrow-left"></i> Quay lại
                                    </button>
                                <?php
                                } else {
                                ?>
                                    <a href="../LoginAndSignup/login.php">
                                        <button type="button" class="btn btn-outline-danger">
                                            <i class="fa-solid fa-cart-plus"></i>Thêm vào giỏ hàng
                                        </button>
                                    </a>
                                    <a href="../LoginAndSignup/login.php">
                                        <button type="button" class="btn btn-danger">
                                            <i class="fas fa-bolt"></i> Mua ngay
                                        </button>
                                    </a>
                                    <button type="button" class="btn btn-secondary" id="back-home" onclick="window.location.href='../../../index.php'">
                                        <i class="fas fa-arrow-left"></i> Quay lại
                                    </button>
                                <?php
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="product-tabs">
            <ul class="nav nav-tabs" id="productTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="description-tab" data-bs-toggle="tab" data-bs-target="#description" type="button" role="tab" aria-controls="description" aria-selected="true">
                        <i class="fas fa-info-circle"></i> Thông tin sản phẩm
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab" aria-controls="reviews" aria-selected="false">
                        <i class="fas fa-star"></i> Đánh giá (<?php echo $number_of_rate; ?>)
                    </button>
                </li>
            </ul>
            <div class="tab-content" id="productTabsContent">
                <div class="tab-pane fade show active" id="description" role="tabpanel" aria-labelledby="description-tab">
                    <div class="product-body description">
                        <div class="description-content">
                            <?php echo $book_info['description']; ?>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="reviews" role="tabpanel" aria-labelledby="reviews-tab">
                    <div class="product-body comment">
                        <div class="rating-summary">
                            <div class="rating-average">
                                <span class="rating-number">5.0</span>
                                <div class="stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <div><?php echo $number_of_rate . " đánh giá"; ?></div>
                            </div>
                            <div class="comment-button">
                                <button type="button" class="btn btn-danger" id="commentBtn" data-bs-toggle="modal" data-bs-target="#commentBackdrop">
                                    <i class="fa-regular fa-pen-to-square"></i>Viết đánh giá
                                </button>
                            </div>
                        </div>

                        <div class="comment-sort">
                            <button type="button" class="btn btn-outline-danger active">
                                <i class="fas fa-sort-amount-down"></i> Mới nhất
                            </button>
                            <button type="button" class="btn btn-outline-danger">
                                <i class="fas fa-star"></i> Đánh giá cao nhất
                            </button>
                        </div>

                        <div class="container comments">
                            <?php
                            $item = 0;
                            if (mysqli_num_rows($cmt_run) > 0) {
                                while ($cmt_info = mysqli_fetch_array($cmt_run)) {
                                    $name = "SELECT `name` FROM `customer` WHERE customer_id=$cmt_info[customer_id]";
                                    $name_run = mysqli_query($conn, $name);
                                    $temp = mysqli_fetch_array($name_run);
                                    if ($item > 7) {
                                        break;
                                    }
                                    echo "
                                    <div class='comment-card'>
                                        <div class='comment-header'>
                                            <div class='commentor-avatar'>
                                                <i class='fas fa-user-circle'></i>
                                            </div>
                                            <div class='commentor-info'>
                                                <strong>$temp[name]</strong>
                                                <div class='comment-stars'>
                                                    <i class='fas fa-star'></i>
                                                    <i class='fas fa-star'></i>
                                                    <i class='fas fa-star'></i>
                                                    <i class='fas fa-star'></i>
                                                    <i class='fas fa-star'></i>
                                                </div>
                                                <div class='comment-date'>2 ngày trước</div>
                                            </div>
                                        </div>
                                        <div class='comment-content'>$cmt_info[comment]</div>
                                    </div>";
                                    $item = $item + 1;
                                }
                            } else {
                                echo "<div class='no-comments'>Chưa có đánh giá nào. Hãy là người đầu tiên đánh giá sản phẩm này!</div>";
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="product-body suggestion">
            <div class="subtitle">
                <i class="fas fa-book"></i> CHILLNFREE BOOKSHOP Đề Xuất
            </div>
            <div class="container">
                <div class="row text-center suggestion-container">
                    <?php
                    include '../components/suggestion-item.php';
                    ?>
                </div>
            </div>
        </div>

        <!-- Modal đánh giá -->
        <div class="modal fade" id="commentBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="commentBackdropLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="commentBackdropLabel">ĐÁNH GIÁ SẢN PHẨM</h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="rating-stars mb-3">
                            <div class="stars-input">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="rating-text">Chọn đánh giá của bạn</div>
                        </div>
                        <label for="newComment" class="form-label">Viết đánh giá của bạn</label>
                        <textarea class="form-control" id="newComment" name="newComment" rows="4" placeholder="Chia sẻ trải nghiệm của bạn về sản phẩm này..."></textarea>
                        <input type="text" hidden="hidden" id="book-id" value="<?= $book_info['book_id'] ?>">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-danger confirm-button" id="newComment-bt" name="newComment-sm">Gửi đánh giá</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php include '../components/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous"></script>
    <script src="../../js/jquery.js"></script>
    <script src="../../js/function.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.0.js"></script>
    <script src="../../js/cart.js"></script>
    <script>
        // Xử lý tăng giảm số lượng
        document.getElementById('decrease-quantity').addEventListener('click', function() {
            var quantityInput = document.getElementById('set-quantity');
            var currentValue = parseInt(quantityInput.value);
            if (currentValue > 1) {
                quantityInput.value = currentValue - 1;
            }
        });

        document.getElementById('increase-quantity').addEventListener('click', function() {
            var quantityInput = document.getElementById('set-quantity');
            var currentValue = parseInt(quantityInput.value);
            quantityInput.value = currentValue + 1;
        });

        // Xử lý đánh giá sao
        const stars = document.querySelectorAll('.stars-input i');
        stars.forEach((star, index) => {
            star.addEventListener('click', () => {
                stars.forEach((s, i) => {
                    if (i <= index) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
                document.querySelector('.rating-text').textContent = `Bạn đã đánh giá ${index + 1} sao`;
            });
        });
    </script>
</body>

</html>