:root {
  --sidebar-bg: #ffffff;
  --sidebar-accent: #4361ee;
  --sidebar-accent-light: #4895ef;
  --sidebar-hover: #f0f4ff;
  --sidebar-active-bg: #e6f0ff;
  --sidebar-text: #4f4f4f;
  --sidebar-active-text: #4361ee;
  --sidebar-shadow: 0 8px 15px rgba(0, 0, 0, 0.06);
  --sidebar-radius: 12px;
  --transition: all 0.3s ease-in-out;
}

.sidebar {
  background-color: var(--sidebar-bg);
  padding: 1.8em;
  margin: 15px 0;
  border-radius: var(--sidebar-radius);
  box-shadow: var(--sidebar-shadow);
  transition: var(--transition);
  width: 260px;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  background: linear-gradient(to bottom, var(--sidebar-accent), var(--sidebar-accent-light));
  border-top-left-radius: var(--sidebar-radius);
  border-bottom-left-radius: var(--sidebar-radius);
}

.sidebar-title {
  font-size: 22px;
  font-weight: 700;
  color: var(--sidebar-accent);
  margin-bottom: 1.5em;
  padding-bottom: 0.6em;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  display: inline-block;
}

.sidebar-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, var(--sidebar-accent), var(--sidebar-accent-light));
  border-radius: 3px;
}

.sidebar-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.sidebar-list li {
  border-radius: 10px;
  overflow: hidden;
  transition: var(--transition);
}

.sidebar-list a {
  display: flex;
  align-items: center;
  padding: 14px 20px;
  text-decoration: none;
  color: var(--sidebar-text);
  font-weight: 500;
  border-radius: 10px;
  transition: var(--transition);
  background-color: transparent;
  position: relative;
}

.sidebar-list a:hover {
  background-color: var(--sidebar-hover);
  color: var(--sidebar-accent);
  transform: translateX(5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.sidebar-list a.active, .sidebar-list a[href="../customer/account.php"] {
  background-color: var(--sidebar-active-bg);
  color: var(--sidebar-active-text);
  font-weight: 600;
  box-shadow: 0 4px 10px rgba(67, 97, 238, 0.15);
}

/* Add icons to sidebar menu items */
.sidebar-list a::before {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  margin-right: 12px;
  font-size: 16px;
  opacity: 0.8;
  transition: var(--transition);
  width: 20px;
  text-align: center;
}

.sidebar-list a[href="../customer/account.php"]::before {
  content: "\f007"; /* user icon */
  color: #4361ee;
}

.sidebar-list a[href="../customer/address.php"]::before {
  content: "\f3c5"; /* map marker icon */
  color: #38b000;
}

.sidebar-list a[href="../customer/order-history.php"]::before {
  content: "\f07a"; /* shopping bag icon */
  color: #ff9e00;
}

.sidebar-list a[href="../customer/comment.php"]::before {
  content: "\f075"; /* comment icon */
  color: #9d4edd;
}

.sidebar-list a:hover::before {
  transform: scale(1.2);
  opacity: 1;
}

/* Responsive for mobile devices */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    padding: 1.5em;
    margin: 10px 0 20px 0;
  }

  .sidebar-title {
    font-size: 20px;
    margin-bottom: 1.2em;
  }

  .sidebar-list {
    gap: 12px;
  }

  .sidebar-list a {
    padding: 12px 18px;
  }
}

@media (max-width: 576px) {
  .sidebar {
    width: 100%;
    padding: 15px;
    margin: 10px 0 15px 0;
    box-sizing: border-box;
  }

  .sidebar-title {
    font-size: 18px;
    margin-bottom: 1em;
    padding-bottom: 0.5em;
  }

  .sidebar-list {
    gap: 10px;
  }

  .sidebar-list a {
    padding: 12px 15px;
    font-size: 15px;
  }

  .sidebar-list a::before {
    margin-right: 10px;
    font-size: 14px;
  }

  .row {
    flex-direction: column;
    gap: 0;
  }

  .col-3, .col-4, .col-9, .col-8 {
    width: 100%;
    max-width: 100%;
    flex: 0 0 100%;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
  }

  .container-fluid.content, .main-container {
    padding: 10px;
    margin: 0;
  }
}
