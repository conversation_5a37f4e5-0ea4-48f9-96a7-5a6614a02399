<?php
include('../../controller/ctmAccount_ctl.php');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bookstore</title>
    <link href="//cdn.muicss.com/mui-0.10.3/css/mui.min.css" rel="stylesheet" type="text/css" />
    <script src="//cdn.muicss.com/mui-0.10.3/js/mui.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
    <link rel="stylesheet" href="../../../public/css/header.css">
    <link rel="stylesheet" href="../../../public/css/footer.css">
    <link rel="stylesheet" href="../../../public/css/account/sidebar.css">
    <link rel="stylesheet" href="../../../public/css/account/order-history.css">
    <link rel="stylesheet" href="../../../public/css/mobile.css">
    <script src="https://kit.fontawesome.com/9d371022aa.js" crossorigin="anonymous"></script>
</head>

<body>
<?php
        if(isset($_SESSION['status'])){
            $p = $_SESSION['status'];
            echo '<script>alert("Mua hàng thành công")</script>';
          unset($_SESSION['status']);
        }
        ?>
    <?php include '../components/header.php'; ?>
    <div class="main-container">
        <div class="container-fluid content">
            <div class="row">
                <div class="col-3">
                    <?php include '../components/customer-sidebar.php'; ?>
                </div>
                <div class="col-9">
                    <div class="history">
                        <p class="history-title">ĐƠN HÀNG CỦA TÔI</p>
                        <?php
                        include('listOder.php');
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    <?php include '../components/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous"></script>
    <script src="../../js/jquery.js"></script>
    <script src="../../js/function.js"></script>

    <script>
        $(document).ready(function() {
            // Xử lý sự kiện khi nhấn nút hủy đơn hàng
            $(document).on('click', '.cancel-order-btn', function() {
                const orderId = $(this).data('order-id');
                const orderRow = $(this).closest('.history-status');

                if (confirm('Bạn có chắc chắn muốn hủy đơn hàng này?')) {
                    // Gửi yêu cầu AJAX để hủy đơn hàng
                    $.ajax({
                        url: '../../../app/controller/cancel_order_ctl.php',
                        type: 'POST',
                        dataType: 'json',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        data: {
                            order_id: orderId
                        },
                        beforeSend: function() {
                            // Hiển thị trạng thái đang xử lý
                            const cancelBtn = $('.cancel-order-btn[data-order-id="' + orderId + '"]');
                            cancelBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang xử lý...');
                        },
                        success: function(response) {
                            if (response.success) {
                                // Cập nhật giao diện khi hủy đơn hàng thành công
                                orderRow.find('.status').removeClass('status-processing').addClass('status-cancelled').text('Trạng thái: Đã hủy');
                                orderRow.find('.cancel-order-btn').remove();

                                // Hiển thị thông báo thành công
                                alert(response.message);
                            } else {
                                // Hiển thị thông báo lỗi
                                alert(response.message);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('AJAX Error:', status, error);
                            console.log('Response:', xhr.responseText);
                            alert('Đã xảy ra lỗi khi kết nối đến máy chủ: ' + status);
                        }
                    });
                }
            });
        });
    </script>
</body>

</html>