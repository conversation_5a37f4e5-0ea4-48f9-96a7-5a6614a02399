:root {
  --primary: #4A90E2;
  --bg-light: #f1f6f5;
  --white: #ffffff;
  --border: #ddd;
  --text-dark: #333;
  --success-bg: #d4f8d4;
  --success-color: #27ae60;
  --shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  --radius: 10px;
  --transition: all 0.3s ease-in-out;
}

body {
  background-color: var(--bg-light);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text-dark);
}

.content {
  width: 90%;
  max-width: 1200px;
  margin: 30px auto;
}

.main-container {
  background-color: var(--bg-light);
}

.detail-title {
  font-weight: 700;
  font-size: 22px;
  color: var(--primary);
  margin-bottom: 1em;
  border-bottom: 2px solid var(--primary);
  padding-bottom: 0.5em;
  letter-spacing: 0.5px;
}

.order-view-info,
.order-view-method,
.order-view-product {
  background-color: var(--white);
  border-radius: var(--radius);
  margin: 15px 0;
  padding: 1.5em;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.order-view-info:hover,
.order-view-method:hover,
.order-view-product:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.08);
}

.order-view-status {
  background-color: var(--success-bg);
  width: fit-content;
  color: var(--success-color);
  padding: 6px 12px;
  border-radius: 6px;
  font-weight: 600;
  margin-bottom: 15px;
  font-size: 14px;
}

.col-content {
  margin-top: 10px;
}

.col-content .detail-title {
  border-bottom: 2px solid var(--primary);
  padding-bottom: 10px;
  font-size: 18px;
}

.product-image {
  object-fit: contain;
  height: 70px;
  border: 1px solid var(--border);
  border-radius: 6px;
  padding: 5px;
  background-color: #f9fcff;
  transition: var(--transition);
}

.product-image:hover {
  transform: scale(1.05);
}

/* Fix col-3/col-9/row for mobile */
@media (max-width: 576px) {
  .row {
    flex-direction: column !important;
    gap: 0 !important;
  }
  .col-3, .col-4, .col-9, .col-8, .col-xl-8, .col-xl-4, .col {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box;
  }
  .order-view-info, .order-view-method, .order-view-product, .order-view-status {
    padding: 10px !important;
    margin: 0 !important;
  }
  .order-title, .order-view-status {
    font-size: 1.1em !important;
  }
}
