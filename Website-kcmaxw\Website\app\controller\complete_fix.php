<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

include('../../app/config.php');

// Hiển thị thông tin hệ thống
echo "<h2>Thông tin hệ thống</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>MySQL Version:</strong> " . $conn->server_info . "</p>";

// Kiểm tra cấu trúc bảng customer
echo "<h3>Cấu trúc bảng customer:</h3>";
$result = $conn->query("DESCRIBE customer");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background-color: #f2f2f2;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Không thể lấy cấu trúc bảng customer: " . $conn->error . "<br>";
}

// Cập nhật cấu trúc bảng
if (isset($_POST['update_table'])) {
    $sql = "ALTER TABLE `customer` MODIFY `pass` varchar(255) NOT NULL";
    if ($conn->query($sql) === TRUE) {
        echo "<div style='color: green; font-weight: bold;'>Cập nhật cấu trúc bảng thành công!</div>";
    } else {
        echo "<div style='color: red; font-weight: bold;'>Lỗi khi cập nhật cấu trúc bảng: " . $conn->error . "</div>";
    }
}

// Sửa lại file login_controller.php
if (isset($_POST['fix_login_controller'])) {
    $file_path = __DIR__ . '/login_controller.php';
    $file_content = '<?php
include(\'../../config.php\');
 if(isset($_SESSION[\'customer_id\'])){
    header(\'location: ../customer/home.php\');
    exit(0);
 }
if(isset($_POST[\'login\'])){
    $email = mysqli_real_escape_string($conn, $_POST[\'email\']);
    $pass = mysqli_real_escape_string($conn, $_POST[\'pass\']);

    // Chỉ kiểm tra email trước
    $sql = "SELECT * FROM customer WHERE email=\'$email\'";
    $result = mysqli_query($conn,$sql);

    if(mysqli_num_rows($result) === 1){
        $row = mysqli_fetch_assoc($result);

        // Kiểm tra mật khẩu bằng password_verify
        if(password_verify($pass, $row[\'pass\'])){
            // Mật khẩu đúng, kiểm tra trạng thái xác thực
            if($row[\'verify_status\'] == 1){
                $_SESSION[\'customer_id\'] = $row[\'customer_id\'];
                $id = $row[\'customer_id\'];
                header("location: ../customer/home.php");
            }else{
                $_SESSION[\'status\'] = "Email chưa xác thực.";
            }
        }else{
            // Thử kiểm tra trực tiếp (cho tài khoản cũ)
            if($pass === $row[\'pass\']){
                // Mật khẩu khớp khi so sánh trực tiếp
                if($row[\'verify_status\'] == 1){
                    $_SESSION[\'customer_id\'] = $row[\'customer_id\'];
                    $id = $row[\'customer_id\'];
                    header("location: ../customer/home.php");
                }else{
                    $_SESSION[\'status\'] = "Email chưa xác thực.";
                }
            }else{
                // Mật khẩu không đúng
                $_SESSION[\'status\'] = "Email hoặc mật khẩu không hợp lệ.";
            }
        }
    }else{
        // Email không tồn tại
        $_SESSION[\'status\'] = "Email hoặc mật khẩu không hợp lệ.";
    }
}
?>';

    if (file_put_contents($file_path, $file_content)) {
        echo "<div style='color: green; font-weight: bold;'>Đã sửa file login_controller.php thành công!</div>";
    } else {
        echo "<div style='color: red; font-weight: bold;'>Lỗi khi sửa file login_controller.php!</div>";
    }
}

// Tạo tài khoản mới hoàn toàn
if (isset($_POST['create_account'])) {
    $name = mysqli_real_escape_string($conn, $_POST['name']);
    $email = mysqli_real_escape_string($conn, $_POST['create_email']);
    $password = mysqli_real_escape_string($conn, $_POST['create_password']);
    $code = md5(rand());
    
    // Kiểm tra email đã tồn tại chưa
    $check_sql = "SELECT * FROM customer WHERE email='$email'";
    $check_result = mysqli_query($conn, $check_sql);
    
    if (mysqli_num_rows($check_result) > 0) {
        echo "<div style='color: red; font-weight: bold;'>Email đã tồn tại trong hệ thống!</div>";
    } else {
        // Mã hóa mật khẩu
        $hashed_password = password_hash($password, PASSWORD_BCRYPT);
        
        // Thêm tài khoản mới
        $insert_sql = "INSERT INTO customer (name, email, pass, code, verify_status, phone) VALUES ('$name', '$email', '$hashed_password', '$code', 1, '')";
        if (mysqli_query($conn, $insert_sql)) {
            echo "<div style='color: green; font-weight: bold;'>Thêm tài khoản mới thành công!</div>";
            
            // Hiển thị thông tin tài khoản vừa tạo
            $new_account_sql = "SELECT * FROM customer WHERE email='$email'";
            $new_account_result = mysqli_query($conn, $new_account_sql);
            if (mysqli_num_rows($new_account_result) === 1) {
                $row = mysqli_fetch_assoc($new_account_result);
                echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>Thông tin tài khoản mới:</h4>";
                echo "<p><strong>ID:</strong> " . $row['customer_id'] . "</p>";
                echo "<p><strong>Email:</strong> " . $row['email'] . "</p>";
                echo "<p><strong>Tên:</strong> " . $row['name'] . "</p>";
                echo "<p><strong>Mật khẩu (đã mã hóa):</strong> " . htmlspecialchars($row['pass']) . "</p>";
                echo "<p><strong>Độ dài mật khẩu:</strong> " . strlen($row['pass']) . " ký tự</p>";
                echo "<p><strong>Trạng thái xác thực:</strong> " . ($row['verify_status'] == 1 ? 'Đã xác thực' : 'Chưa xác thực') . "</p>";
                echo "</div>";
            }
        } else {
            echo "<div style='color: red; font-weight: bold;'>Lỗi khi thêm tài khoản mới: " . mysqli_error($conn) . "</div>";
        }
    }
}

// Sửa lại file signup_controller.php
if (isset($_POST['fix_signup_controller'])) {
    $file_path = __DIR__ . '/signup_controller.php';
    $file_content = '<?php

    include(\'../../config.php\');
    include(\'../../model/signup_model.php\');
    include(\'../../model/sendMail_model.php\');
    if(isset($_SESSION[\'customer_id\'])){
        header(\'location: ../customer\');
        exit(0);
     }
    $name ="";
    $email ="";
    $succMess="";
    if(isset($_POST[\'signup\']))
    {
        $name = mysqli_real_escape_string($conn, $_POST[\'name\']);
        $email = mysqli_real_escape_string($conn, $_POST[\'mail\']);
        $pass = mysqli_real_escape_string($conn, $_POST[\'pass\']);
        $cpass = mysqli_real_escape_string($conn, $_POST[\'cpass\']);
        $code = mysqli_real_escape_string($conn, md5(rand()));

        // Hash the password before saving it to the database
        $hashedPass = password_hash($pass, PASSWORD_BCRYPT);

        $mess = checkValid($name, $email, $pass, $cpass, $conn);
        if(empty($mess)){
            $sql = "INSERT INTO customer(name,email,pass,code,verify_status) VALUES (\'$name\',\'$email\',\'$hashedPass\',\'$code\',1)";
            $result = mysqli_query($conn,$sql);
            if($result){
                sendmail_Verify( $email, $code);
                $succMess="Đăng kí thành công. Tài khoản đã được xác thực.";
                $name ="";
                $email ="";
                
            }
        }
    
    }
?>';

    if (file_put_contents($file_path, $file_content)) {
        echo "<div style='color: green; font-weight: bold;'>Đã sửa file signup_controller.php thành công!</div>";
    } else {
        echo "<div style='color: red; font-weight: bold;'>Lỗi khi sửa file signup_controller.php!</div>";
    }
}

// Tạo tài khoản test
if (isset($_POST['create_test_account'])) {
    $name = "Test User";
    $email = "<EMAIL>";
    $password = "123456";
    $code = md5(rand());
    
    // Mã hóa mật khẩu
    $hashed_password = password_hash($password, PASSWORD_BCRYPT);
    
    // Kiểm tra email đã tồn tại chưa
    $check_sql = "SELECT * FROM customer WHERE email='$email'";
    $check_result = mysqli_query($conn, $check_sql);
    
    if (mysqli_num_rows($check_result) > 0) {
        echo "<div style='color: orange; font-weight: bold;'>Email đã tồn tại trong hệ thống!</div>";
        
        // Cập nhật mật khẩu
        $update_sql = "UPDATE customer SET pass='$hashed_password', verify_status=1 WHERE email='$email'";
        if (mysqli_query($conn, $update_sql)) {
            echo "<div style='color: green; font-weight: bold;'>Đã cập nhật mật khẩu cho tài khoản test!</div>";
        } else {
            echo "<div style='color: red; font-weight: bold;'>Lỗi khi cập nhật mật khẩu: " . mysqli_error($conn) . "</div>";
        }
    } else {
        // Thêm tài khoản mới
        $insert_sql = "INSERT INTO customer (name, email, pass, code, verify_status, phone) VALUES ('$name', '$email', '$hashed_password', '$code', 1, '')";
        if (mysqli_query($conn, $insert_sql)) {
            echo "<div style='color: green; font-weight: bold;'>Đã tạo tài khoản test thành công!</div>";
        } else {
            echo "<div style='color: red; font-weight: bold;'>Lỗi khi tạo tài khoản test: " . mysqli_error($conn) . "</div>";
        }
    }
    
    // Hiển thị thông tin tài khoản
    $account_sql = "SELECT * FROM customer WHERE email='$email'";
    $account_result = mysqli_query($conn, $account_sql);
    if (mysqli_num_rows($account_result) === 1) {
        $row = mysqli_fetch_assoc($account_result);
        echo "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>Thông tin tài khoản test:</h4>";
        echo "<p><strong>ID:</strong> " . $row['customer_id'] . "</p>";
        echo "<p><strong>Email:</strong> " . $row['email'] . "</p>";
        echo "<p><strong>Tên:</strong> " . $row['name'] . "</p>";
        echo "<p><strong>Mật khẩu (đã mã hóa):</strong> " . htmlspecialchars($row['pass']) . "</p>";
        echo "<p><strong>Độ dài mật khẩu:</strong> " . strlen($row['pass']) . " ký tự</p>";
        echo "<p><strong>Trạng thái xác thực:</strong> " . ($row['verify_status'] == 1 ? 'Đã xác thực' : 'Chưa xác thực') . "</p>";
        echo "</div>";
        
        echo "<div style='background-color: #e9f7ef; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>Thông tin đăng nhập:</h4>";
        echo "<p><strong>Email:</strong> <EMAIL></p>";
        echo "<p><strong>Mật khẩu:</strong> 123456</p>";
        echo "<a href='../views/LoginAndSignup/login.php' style='display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px;'>Đi đến trang đăng nhập</a>";
        echo "</div>";
    }
}

// Cập nhật tất cả mật khẩu
if (isset($_POST['update_all_passwords'])) {
    $default_password = "123456";
    $hashed_password = password_hash($default_password, PASSWORD_BCRYPT);
    
    $update_sql = "UPDATE customer SET pass='$hashed_password', verify_status=1";
    if (mysqli_query($conn, $update_sql)) {
        echo "<div style='color: green; font-weight: bold;'>Đã cập nhật mật khẩu cho tất cả tài khoản!</div>";
        echo "<div style='background-color: #e9f7ef; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>Thông tin đăng nhập mới cho tất cả tài khoản:</h4>";
        echo "<p><strong>Mật khẩu:</strong> 123456</p>";
        echo "</div>";
    } else {
        echo "<div style='color: red; font-weight: bold;'>Lỗi khi cập nhật mật khẩu: " . mysqli_error($conn) . "</div>";
    }
}
?>

<h2>Công cụ sửa lỗi toàn diện</h2>

<div style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border-left: 5px solid #007bff;">
    <h3>1. Cập nhật cấu trúc bảng</h3>
    <p>Cập nhật độ dài trường mật khẩu trong bảng customer từ varchar(50) thành varchar(255).</p>
    <form method="post" action="">
        <input type="submit" name="update_table" value="Cập nhật cấu trúc bảng" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
    </form>
</div>

<div style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border-left: 5px solid #28a745;">
    <h3>2. Sửa file login_controller.php</h3>
    <p>Sửa file login_controller.php để hỗ trợ cả mật khẩu đã mã hóa và mật khẩu chưa mã hóa.</p>
    <form method="post" action="">
        <input type="submit" name="fix_login_controller" value="Sửa file login_controller.php" style="padding: 10px 20px; background-color: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
    </form>
</div>

<div style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border-left: 5px solid #17a2b8;">
    <h3>3. Sửa file signup_controller.php</h3>
    <p>Sửa file signup_controller.php để tự động xác thực tài khoản mới.</p>
    <form method="post" action="">
        <input type="submit" name="fix_signup_controller" value="Sửa file signup_controller.php" style="padding: 10px 20px; background-color: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer;">
    </form>
</div>

<div style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border-left: 5px solid #ffc107;">
    <h3>4. Tạo tài khoản test</h3>
    <p>Tạo tài khoản test đã được xác thực.</p>
    <form method="post" action="">
        <input type="submit" name="create_test_account" value="Tạo tài khoản test" style="padding: 10px 20px; background-color: #ffc107; color: black; border: none; border-radius: 4px; cursor: pointer;">
    </form>
</div>

<div style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border-left: 5px solid #dc3545;">
    <h3>5. Cập nhật tất cả mật khẩu</h3>
    <p>Cập nhật mật khẩu cho tất cả tài khoản thành "123456" và đặt trạng thái xác thực thành 1.</p>
    <form method="post" action="">
        <input type="submit" name="update_all_passwords" value="Cập nhật tất cả mật khẩu" style="padding: 10px 20px; background-color: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
    </form>
</div>

<div style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; border-left: 5px solid #6c757d;">
    <h3>6. Tạo tài khoản mới</h3>
    <p>Tạo tài khoản mới đã được xác thực.</p>
    <form method="post" action="">
        <div style="margin-bottom: 10px;">
            <label for="name">Tên:</label>
            <input type="text" name="name" required style="padding: 8px; width: 300px; border: 1px solid #ced4da; border-radius: 4px;">
        </div>
        <div style="margin-bottom: 10px;">
            <label for="create_email">Email:</label>
            <input type="text" name="create_email" required style="padding: 8px; width: 300px; border: 1px solid #ced4da; border-radius: 4px;">
        </div>
        <div style="margin-bottom: 10px;">
            <label for="create_password">Mật khẩu:</label>
            <input type="password" name="create_password" required style="padding: 8px; width: 300px; border: 1px solid #ced4da; border-radius: 4px;">
        </div>
        <input type="submit" name="create_account" value="Tạo tài khoản mới" style="padding: 10px 20px; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
    </form>
</div>

<h3>Danh sách tài khoản:</h3>
<?php
// Hiển thị danh sách tài khoản
$result = $conn->query("SELECT customer_id, email, name, LENGTH(pass) as pass_length, verify_status FROM customer");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f2f2f2;'><th>ID</th><th>Email</th><th>Name</th><th>Password Length</th><th>Verify Status</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $row['customer_id'] . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $row['email'] . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $row['name'] . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . $row['pass_length'] . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . ($row['verify_status'] == 1 ? 'Đã xác thực' : 'Chưa xác thực') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Không thể lấy dữ liệu từ bảng customer: " . $conn->error . "<br>";
}
?>
